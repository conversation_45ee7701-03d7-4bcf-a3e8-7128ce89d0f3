<?php
/**
 * Validation Utilities
 * Provides form validation and schema checking
 */

function validate_required($value, $field_name = 'Field') {
    if (is_null($value) || $value === '' || $value === []) {
        return "{$field_name} is required";
    }
    return null;
}

function validate_email($email, $field_name = 'Email') {
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return "{$field_name} must be a valid email address";
    }
    return null;
}

function validate_numeric($value, $field_name = 'Field', $min = null, $max = null) {
    if (!is_numeric($value)) {
        return "{$field_name} must be a number";
    }
    
    $num = floatval($value);
    if ($min !== null && $num < $min) {
        return "{$field_name} must be at least {$min}";
    }
    
    if ($max !== null && $num > $max) {
        return "{$field_name} must not exceed {$max}";
    }
    
    return null;
}

function validate_integer($value, $field_name = 'Field', $min = null, $max = null) {
    if (!is_numeric($value) || !is_int($value + 0)) {
        return "{$field_name} must be an integer";
    }
    
    $int = intval($value);
    if ($min !== null && $int < $min) {
        return "{$field_name} must be at least {$min}";
    }
    
    if ($max !== null && $int > $max) {
        return "{$field_name} must not exceed {$max}";
    }
    
    return null;
}

function validate_enum($value, array $allowed, $field_name = 'Field') {
    if (!in_array($value, $allowed, true)) {
        $options = implode(', ', $allowed);
        return "{$field_name} must be one of: {$options}";
    }
    return null;
}

function validate_length($value, $field_name = 'Field', $min = null, $max = null) {
    $length = strlen($value);
    
    if ($min !== null && $length < $min) {
        return "{$field_name} must be at least {$min} characters";
    }
    
    if ($max !== null && $length > $max) {
        return "{$field_name} must not exceed {$max} characters";
    }
    
    return null;
}

function validate_date($value, $field_name = 'Date', $format = 'Y-m-d') {
    $date = DateTime::createFromFormat($format, $value);
    if (!$date || $date->format($format) !== $value) {
        return "{$field_name} must be a valid date in format {$format}";
    }
    return null;
}

function validate_form_data(array $data, array $rules) {
    $errors = [];
    
    foreach ($rules as $field => $field_rules) {
        $value = $data[$field] ?? null;
        $field_name = $field_rules['name'] ?? ucfirst($field);
        
        // Check required
        if (!empty($field_rules['required'])) {
            $error = validate_required($value, $field_name);
            if ($error) {
                $errors[$field] = $error;
                continue; // Skip other validations if required fails
            }
        }
        
        // Skip further validation if field is empty and not required
        if (is_null($value) || $value === '') {
            continue;
        }
        
        // Type validation
        if (isset($field_rules['type'])) {
            switch ($field_rules['type']) {
                case 'email':
                    $error = validate_email($value, $field_name);
                    break;
                case 'numeric':
                    $error = validate_numeric($value, $field_name, 
                        $field_rules['min'] ?? null, $field_rules['max'] ?? null);
                    break;
                case 'integer':
                    $error = validate_integer($value, $field_name,
                        $field_rules['min'] ?? null, $field_rules['max'] ?? null);
                    break;
                case 'date':
                    $error = validate_date($value, $field_name, 
                        $field_rules['format'] ?? 'Y-m-d');
                    break;
                case 'string':
                    $error = validate_length($value, $field_name,
                        $field_rules['min_length'] ?? null, $field_rules['max_length'] ?? null);
                    break;
                default:
                    $error = null;
            }
            
            if ($error) {
                $errors[$field] = $error;
                continue;
            }
        }
        
        // Enum validation
        if (isset($field_rules['enum'])) {
            $error = validate_enum($value, $field_rules['enum'], $field_name);
            if ($error) {
                $errors[$field] = $error;
            }
        }
        
        // Custom validation
        if (isset($field_rules['custom']) && is_callable($field_rules['custom'])) {
            $error = $field_rules['custom']($value, $field_name);
            if ($error) {
                $errors[$field] = $error;
            }
        }
    }
    
    return $errors;
}

function validate_submission_data(array $data, $user = null) {
    // Get current user if not provided
    if (!$user) {
        $user = get_current_app_user();
    }

    $is_rzm = $user && isset($user['role']) && $user['role'] === 'RZM';

    $rules = [
        'entity_type' => [
            'name' => 'Entity Type',
            'required' => true,
            'type' => 'string',
            'enum' => ['Campaigns', 'CategoryB', 'CategoryC', 'Cell', 'Adult', 'Kids', 'Teens', 'External']
        ],
        'region_id' => [
            'name' => 'Region',
            'required' => !$is_rzm, // Not required for RZM users
            'type' => 'integer',
            'min' => 1,
            'max' => 6
        ],
        'zone_id' => [
            'name' => 'Zone',
            'required' => !$is_rzm, // Not required for RZM users
            'type' => 'string',
            'min_length' => 1
        ],
        'country.code' => [
            'name' => 'Country Code',
            'required' => true,
            'type' => 'string',
            'min_length' => 2,
            'max_length' => 3
        ],
        'money.denomination' => [
            'name' => 'Money Denomination',
            'required' => true,
            'enum' => ['CURRENCY', 'ESPEES']
        ],
        'money.amount' => [
            'name' => 'Amount',
            'required' => true,
            'type' => 'numeric',
            'min' => 0
        ]
    ];
    
    // Flatten nested data for validation
    $flat_data = [];
    array_walk_recursive($data, function($value, $key) use (&$flat_data, $data) {
        $flat_data[$key] = $value;
    });
    
    // Handle period specially
    if (isset($data['period'])) {
        $flat_data['period.month'] = $data['period']['month'] ?? null;
        $flat_data['period.year'] = $data['period']['year'] ?? null;
    }
    
    // Handle country specially
    if (isset($data['country'])) {
        $flat_data['country.code'] = $data['country']['code'] ?? null;
    }
    
    // Handle money specially
    if (isset($data['money'])) {
        $flat_data['money.denomination'] = $data['money']['denomination'] ?? null;
        $flat_data['money.amount'] = $data['money']['amount'] ?? null;
    }
    
    return validate_form_data($flat_data, $rules);
}

function sanitize_input($input) {
    if (is_array($input)) {
        return array_map('sanitize_input', $input);
    }
    
    if (is_string($input)) {
        return trim(htmlspecialchars($input, ENT_QUOTES, 'UTF-8'));
    }
    
    return $input;
}

function sanitize_form_data(array $data) {
    return sanitize_input($data);
}
