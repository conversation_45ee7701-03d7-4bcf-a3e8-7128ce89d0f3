<?php
/**
 * Authentication and Session Management
 * Handles login/logout and role-based access
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/db.php';

function login($email, $password) {
    $users = db_read('users');

    foreach ($users as $user) {
        if (($user['email'] ?? '') === $email) {
            if (password_verify($password, $user['password_hash'] ?? '')) {
                // Check if user is approved
                $approval_status = $user['approval_status'] ?? 'approved';
                if ($approval_status === 'denied') {
                    // Log login attempt for denied user
                    audit_log($user['id'], 'login_denied', ['email' => $email, 'status' => $approval_status]);
                    return false;
                } elseif ($approval_status === 'pending') {
                    // Log login attempt for pending user and redirect to pending page
                    audit_log($user['id'], 'login_pending', ['email' => $email, 'status' => $approval_status]);
                    header('Location: ?r=pending_approval');
                    exit;
                }

                // Check if user account is active
                $status = $user['status'] ?? 'active';
                if ($status !== 'active') {
                    // Log login attempt for inactive user
                    audit_log($user['id'], 'login_inactive', ['email' => $email, 'status' => $status]);
                    return false;
                }

                $_SESSION['user'] = $user;
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));

                // Log successful login
                audit_log($user['id'], 'login', ['email' => $email]);

                return true;
            } else {
                // Log failed login attempt
                audit_log('unknown', 'login_failed', ['email' => $email]);
                return false;
            }
        }
    }

    return false;
}

function logout() {
    if (isset($_SESSION['user'])) {
        audit_log($_SESSION['user']['id'], 'logout', []);
    }
    
    session_destroy();
    session_start();
}

function require_login() {
    if (empty($_SESSION['user'])) {
        if (is_ajax_request()) {
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Authentication required']);
            exit;
        } else {
            header('Location: ?r=login');
            exit;
        }
    }
}

function is_admin() {
    return ($_SESSION['user']['role'] ?? '') === 'ADMIN';
}

function require_admin() {
    require_login();
    if (!is_admin()) {
        if (is_ajax_request()) {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Admin access required']);
            exit;
        } else {
            http_response_code(403);
            echo "Access denied. Admin privileges required.";
            exit;
        }
    }
}

function get_current_app_user() {
    return $_SESSION['user'] ?? null;
}

function get_user_id() {
    return $_SESSION['user']['id'] ?? null;
}

function rzm_scope() {
    $user = get_current_app_user();
    if (!$user || is_admin()) {
        return null; // Admin has no scope restrictions
    }
    
    return [
        'region_id' => $user['region_id'] ?? null,
        'zone_id' => $user['zone_id'] ?? null
    ];
}

function is_ajax_request() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// CSRF Protection
function generate_csrf_token() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return !empty($_SESSION['csrf_token']) && 
           hash_equals($_SESSION['csrf_token'], $token);
}

function csrf_token_input() {
    $token = generate_csrf_token();
    return "<input type='hidden' name='csrf_token' value='{$token}'>";
}

function require_csrf() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        if (!verify_csrf_token($token)) {
            if (is_ajax_request()) {
                http_response_code(403);
                header('Content-Type: application/json');
                echo json_encode(['error' => 'Invalid CSRF token']);
                exit;
            } else {
                http_response_code(403);
                echo "Invalid CSRF token";
                exit;
            }
        }
    }
}

// Audit logging
function audit_log($user_id, $action, $details = []) {
    $log_entry = [
        'timestamp' => date('c'),
        'user_id' => $user_id,
        'action' => $action,
        'details' => $details,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];

    $log_line = json_encode($log_entry) . "\n";
    $log_file = __DIR__ . '/../logs/audit.log';

    try {
        // Ensure logs directory exists
        $log_dir = dirname($log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }

        // Attempt to write to log file
        if (file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX) === false) {
            // If writing fails, log to PHP error log as fallback
            error_log("Audit log write failed: " . json_encode($log_entry));
        }
    } catch (Exception $e) {
        // If all else fails, log to PHP error log
        error_log("Audit log exception: " . $e->getMessage() . " - Data: " . json_encode($log_entry));
    }
}
