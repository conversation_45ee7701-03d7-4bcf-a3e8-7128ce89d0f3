<?php
/**
 * Access Control and Scoping
 * Handles RZM scoping and permission checks
 */

require_once __DIR__ . '/auth.php';

function apply_scope(array $rows): array {
    if (is_admin()) {
        return $rows; // Admin sees everything
    }
    
    $scope = rzm_scope();
    if (!$scope) {
        return $rows; // No scope restrictions
    }
    
    return array_values(array_filter($rows, function($record) use ($scope) {
        $record_region = $record['region_id'] ?? null;
        $record_zone = $record['zone_id'] ?? null;
        
        // Match both region and zone
        return $record_region == $scope['region_id'] && 
               $record_zone == $scope['zone_id'];
    }));
}

function can_edit_record(array $record): bool {
    // Admin can edit everything
    if (is_admin()) {
        return true;
    }
    
    // Check if record is in user's scope
    $scope = rzm_scope();
    if (!$scope) {
        return false;
    }
    
    $record_region = $record['region_id'] ?? null;
    $record_zone = $record['zone_id'] ?? null;
    
    if ($record_region != $scope['region_id'] || $record_zone != $scope['zone_id']) {
        return false;
    }
    
    // Check record status - only Draft and Rejected records can be edited by RZM
    $status = $record['status']['state'] ?? 'Draft';
    return in_array($status, ['Draft', 'Rejected']);
}

function can_submit_record(array $record): bool {
    if (!can_edit_record($record)) {
        return false;
    }
    
    // Can submit if it's Draft or Rejected
    $status = $record['status']['state'] ?? 'Draft';
    return in_array($status, ['Draft', 'Rejected']);
}

function can_approve_record(array $record): bool {
    // Only admin can approve
    if (!is_admin()) {
        return false;
    }
    
    // Can only approve Submitted records
    $status = $record['status']['state'] ?? 'Draft';
    return $status === 'Submitted';
}

function can_reject_record(array $record): bool {
    // Only admin can reject
    if (!is_admin()) {
        return false;
    }
    
    // Can only reject Submitted records
    $status = $record['status']['state'] ?? 'Draft';
    return $status === 'Submitted';
}

function get_user_regions(): array {
    if (is_admin()) {
        $lookups = db_read('lookups');
        return $lookups['regions'] ?? [1, 2, 3, 4, 5, 6];
    }
    
    $user = get_current_app_user();
    $region_id = $user['region_id'] ?? null;
    return $region_id ? [$region_id] : [];
}

function get_user_zones(): array {
    if (is_admin()) {
        $lookups = db_read('lookups');
        return $lookups['zones'] ?? [];
    }
    
    $user = get_current_app_user();
    $zone_id = $user['zone_id'] ?? null;
    return $zone_id ? [$zone_id] : [];
}

function filter_by_permissions(array $records, string $permission = 'view'): array {
    return array_filter($records, function($record) use ($permission) {
        switch ($permission) {
            case 'edit':
                return can_edit_record($record);
            case 'submit':
                return can_submit_record($record);
            case 'approve':
                return can_approve_record($record);
            case 'reject':
                return can_reject_record($record);
            case 'view':
            default:
                return true; // Viewing is handled by apply_scope
        }
    });
}

function ensure_record_ownership(array &$record): void {
    $user = get_current_app_user();
    if (!$user) {
        throw new Exception('User not authenticated');
    }
    
    // For new records, set the user's region and zone
    if (empty($record['region_id']) && !is_admin()) {
        $record['region_id'] = $user['region_id'] ?? null;
    }
    
    if (empty($record['zone_id']) && !is_admin()) {
        $record['zone_id'] = $user['zone_id'] ?? null;
    }
    
    // Verify the user can access this record
    if (!is_admin()) {
        $scope = rzm_scope();
        if ($scope) {
            if ($record['region_id'] != $scope['region_id'] || 
                $record['zone_id'] != $scope['zone_id']) {
                throw new Exception('Access denied: Record outside user scope');
            }
        }
    }
}
