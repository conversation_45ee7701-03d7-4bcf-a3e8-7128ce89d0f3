<?php
/**
 * Currency and ESPEES Normalization
 * Handles conversion from local currency/ESPEES to global ESPEES
 */

require_once __DIR__ . '/db.php';

function normalize_record(array $record, array $lookups = null): array {
    if ($lookups === null) {
        $lookups = db_read('lookups');
    }
    
    $country = find_country($lookups, $record['country']['code'] ?? null);
    if (!$country) {
        // If no country found, return record unchanged
        return $record;
    }
    
    $fx_rate = floatval($country['fx_to_local_espees'] ?? 1.0);
    $local_to_global = floatval($country['local_espees_to_global'] ?? 1.0);
    
    // Normalize total money amount
    $money_amount = floatval($record['money']['amount'] ?? 0);
    $denomination = $record['money']['denomination'] ?? 'CURRENCY';
    
    if ($denomination === 'CURRENCY') {
        // Convert currency to local ESPEES first
        $local_espees = $money_amount * $fx_rate;
    } else {
        // Already in ESPEES (local)
        $local_espees = $money_amount;
    }
    
    // Convert local ESPEES to global ESPEES
    $global_espees = $local_espees * $local_to_global;
    
    // Load program registry for per-program normalization
    $program_registry = db_read('program_registry');
    $programs = $program_registry['programs'] ?? [];
    
    $normalized_programs = [];
    foreach ($programs as $program) {
        $key = $program['key'] ?? '';
        if (!$key) continue;
        
        $measure_value = floatval($record['measures'][$key] ?? 0);
        
        if (($program['type'] ?? 'count') === 'money') {
            // Apply same normalization as total money
            if ($denomination === 'CURRENCY') {
                $program_local_espees = $measure_value * $fx_rate;
            } else {
                $program_local_espees = $measure_value;
            }
            $normalized_programs[$key] = round($program_local_espees * $local_to_global, 6);
        } else {
            // For count-type programs, keep as is (no monetary conversion)
            $normalized_programs[$key] = $measure_value;
        }
    }
    
    // Update the record with normalized values
    $record['measures_norm'] = [
        'espees' => round($global_espees, 6),
        'programs' => $normalized_programs
    ];
    
    $record['rates_version'] = $lookups['rates_version'] ?? date('Y-m-d');
    
    return $record;
}

function find_country(array $lookups, ?string $country_code): ?array {
    if (!$country_code) {
        return null;
    }
    
    $countries = $lookups['countries'] ?? [];
    foreach ($countries as $country) {
        if (($country['code'] ?? '') === $country_code) {
            return $country;
        }
    }
    
    return null;
}

function batch_normalize_collection($collection_name) {
    $records = db_read($collection_name);
    $lookups = db_read('lookups');
    $updated_count = 0;
    
    foreach ($records as $index => $record) {
        $normalized = normalize_record($record, $lookups);
        if ($normalized !== $record) {
            $records[$index] = $normalized;
            $updated_count++;
        }
    }
    
    if ($updated_count > 0) {
        db_write($collection_name, $records);
    }
    
    return $updated_count;
}

function recompute_all_normalizations() {
    $collections = [
        'campaigns',

        'category_b', 
        'category_c',
        'cells',
        'partners_adult',
        'partners_kids',
        'partners_teens',
        'partners_external'
    ];
    
    $summary = [];
    $total_updated = 0;
    
    foreach ($collections as $collection) {
        try {
            $updated = batch_normalize_collection($collection);
            $summary[$collection] = $updated;
            $total_updated += $updated;
        } catch (Exception $e) {
            $summary[$collection] = "Error: " . $e->getMessage();
        }
    }
    
    return [
        'total_updated' => $total_updated,
        'collections' => $summary,
        'timestamp' => date('c')
    ];
}

function get_currency_options() {
    $lookups = db_read('lookups');
    $currencies = [];
    
    foreach ($lookups['countries'] ?? [] as $country) {
        $code = $country['currency_code'] ?? '';
        $name = $country['name'] ?? '';
        if ($code && !isset($currencies[$code])) {
            $currencies[$code] = $name;
        }
    }
    
    return $currencies;
}

function get_country_options() {
    $lookups = db_read('lookups');
    $countries = [];
    
    foreach ($lookups['countries'] ?? [] as $country) {
        $code = $country['code'] ?? '';
        $name = $country['name'] ?? '';
        if ($code && $name) {
            $countries[$code] = $name;
        }
    }
    
    return $countries;
}
