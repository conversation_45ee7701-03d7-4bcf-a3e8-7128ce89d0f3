<?php
/**
 * JSON DB Utilities with File Locking
 * Provides thread-safe JSON file operations
 */

function db_path($name) {
    return __DIR__ . "/../data/{$name}.json";
}

function db_read($name) {
    $path = db_path($name);
    if (!file_exists($path)) {
        return [];
    }
    
    $content = file_get_contents($path);
    if ($content === false) {
        throw new Exception("Failed to read file: {$name}");
    }
    
    $data = json_decode($content, true);
    return $data ?: [];
}

function db_write($name, $array) {
    $path = db_path($name);
    $dir = dirname($path);
    
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $file = fopen($path, 'c+');
    if (!$file) {
        throw new Exception("Failed to open file for writing: {$name}");
    }
    
    if (!flock($file, LOCK_EX)) {
        fclose($file);
        throw new Exception("Failed to acquire lock on file: {$name}");
    }
    
    ftruncate($file, 0);
    rewind($file);
    
    $json = json_encode($array, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if (fwrite($file, $json) === false) {
        flock($file, LOCK_UN);
        fclose($file);
        throw new Exception("Failed to write to file: {$name}");
    }
    
    fflush($file);
    flock($file, LOCK_UN);
    fclose($file);
}

function db_append($name, $item) {
    $array = db_read($name);
    $array[] = $item;
    db_write($name, $array);
}

function db_update($name, $id, $updates) {
    $array = db_read($name);
    $found = false;
    
    foreach ($array as $index => $item) {
        if (($item['id'] ?? '') === $id) {
            $array[$index] = array_merge($item, $updates);
            $found = true;
            break;
        }
    }
    
    if ($found) {
        db_write($name, $array);
        return true;
    }
    
    return false;
}

function db_find($name, $id) {
    $array = db_read($name);
    foreach ($array as $item) {
        if (($item['id'] ?? '') === $id) {
            return $item;
        }
    }
    return null;
}

function db_delete($name, $id) {
    $array = db_read($name);
    $newArray = array_values(array_filter($array, function($item) use ($id) {
        return ($item['id'] ?? '') !== $id;
    }));
    
    if (count($newArray) !== count($array)) {
        db_write($name, $newArray);
        return true;
    }
    
    return false;
}

function generate_ulid() {
    // Simple ULID-like ID generator
    $timestamp = str_pad(base_convert(time(), 10, 36), 10, '0', STR_PAD_LEFT);
    $random = str_pad(base_convert(mt_rand(0, 1679615), 10, 36), 16, '0', STR_PAD_LEFT);
    return strtoupper($timestamp . $random);
}
