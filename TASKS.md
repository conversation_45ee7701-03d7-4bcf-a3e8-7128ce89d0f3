# IPPC Awards Reporting Portal — Task List (Simple Structure, no MVC)

A checkbox-ready plan using a flat, minimal structure: a single index, page includes, helper libs, and lightweight API scripts (no controllers/views folders).

## Folder Layout (Simplified)
- [ ] `lib/` — helpers: `auth.php`, `db.php`, `normalize.php`, `validators.php`, `access.php`, `components.php`, `layout.php`, `utils.php`.
- [ ] `pages/` — page files: `login.php`, `dashboard.php`, `submit_category_b.php`, `submit_adult.php`, `admin/` (e.g., `settings.php`, `pending.php`).
- [ ] `api/` — endpoint scripts: `search.php`, `top.php`, `submit.php`, `approve.php`, `reject.php`, `admin_save.php`, `recompute.php`.
- [ ] `public/` — `index.php`, `assets/`.
- [ ] `data/` — JSON collections + registries; `data/forms/`, `data/schemas/`.
- [ ] `logs/` — `audit.log`.

## Project Scaffolding
- [ ] Create folders above; ensure PHP 8+; make `data/` and `logs/` writable.
- [ ] `public/index.php` — minimal router: reads `$_GET['p']` (default `dashboard`), requires `lib/auth.php`, `lib/db.php`, `lib/normalize.php`, `lib/access.php`, then includes `pages/{p}.php`.
- [ ] Add shared shell: `lib/layout.php` (head with CDNs, header/nav, footer), `lib/components.php` (inputs, filters, toasts).

## CDNs & Theme
- [ ] In `lib/layout.php`, include Tailwind CDN (+ primary color extend), jQuery + DataTables, and SweetAlert2.
- [ ] Apply minimalist, non‑gradient styles (system font, neutral colors, rounded cards/buttons, subtle shadow).

## Data Seeds
- [ ] `data/users.json` — ADMIN and RZM entries (`password_hash()` for passwords).
- [ ] `data/program_registry.json` — program keys, labels, enabled, `type: money|count`.
- [ ] `data/category_registry.json` — categories, allowed programs, form/schema refs.
- [ ] `data/lookups.json` — regions/zones and countries with `fx_to_local_espees`, `local_espees_to_global`, `rates_version`.
- [ ] Empty collections: `campaigns.json`, `category_b.json`, `category_c.json`, `cells.json`, `partners_adult.json`, `partners_kids.json`, `partners_teens.json`, `partners_external.json`.

## Core Utilities (lib)
- [ ] `lib/db.php` — `db_path`, `db_read`, `db_write`, `db_append` (locking + pretty JSON).
- [ ] `lib/auth.php` — `session_start`, `login`, `logout`, `require_login`, `is_admin`, `rzm_scope`.
- [ ] `lib/access.php` — `apply_scope($rows)`; Admin bypasses, RZM filtered by `region_id` + `zone_id`.
- [ ] `lib/normalize.php` — `find_country`, `normalize_record` (money → country ESPEES → global ESPEES; per‑program normalization; set `rates_version`).
- [ ] `lib/validators.php` — required, enum, numeric ranges; simple schema runner for form configs.
- [ ] `lib/utils.php` — CSRF helpers (make/verify), ULID/ID helper, date helpers.

## Pages (includes)
- [ ] `pages/login.php` — email/password form; CSRF; uses `lib/auth.php` to log in; SweetAlert2 errors; redirects to dashboard.
- [ ] `pages/dashboard.php` — KPIs, filters, DataTable shell, Top‑N widgets; loads data via `/api/search.php` and `/api/top.php`.
- [ ] `pages/submit_category_b.php` — renders from `data/forms/category_b.json` and posts to `/api/submit.php`.
- [ ] `pages/submit_adult.php` — renders from `data/forms/partners_adult.json` and posts to `/api/submit.php`.
- [ ] `pages/admin/settings.php` — editors for registries (`program_registry.json`, `category_registry.json`, `lookups.json`) saving to `/api/admin_save.php`; show Recompute button -> `/api/recompute.php`.
- [ ] `pages/admin/pending.php` — list Submitted records with Approve/Reject buttons calling `/api/approve.php` and `/api/reject.php`.

## API Scripts (flat endpoints)
- [ ] `api/search.php` — JSON search: loads collections, applies scope/filters, sorting, pagination.
- [ ] `api/top.php` — JSON Top‑N: aggregates by `zone|group|church|person|country`, metric `espees` or `program:<key>`.
- [ ] `api/submit.php` — accepts POST: CSRF, validate, build record, `normalize_record`, set status Submitted, append to collection, return JSON.
- [ ] `api/approve.php` — Admin only: set `approved_at/by` for a record.
- [ ] `api/reject.php` — Admin only: set `rejected_at/by` and `rejection_reason`.
- [ ] `api/admin_save.php` — Admin only: save registries; if `lookups.json` saved, bump `rates_version`.
- [ ] `api/recompute.php` — Admin only: iterate all collections, re‑normalize, persist, return summary.

## Form Handling (Submit → JSON)
- [ ] Load form config from `data/forms/*.json` to render inputs.
- [ ] Validate server‑side in `api/submit.php` using `lib/validators.php`.
- [ ] Build record with core model (id, entity_type, period, region/zone, country, money, measures, status, audit).
- [ ] Normalize via `lib/normalize.php`; write to correct collection file.
- [ ] Respond with JSON; page shows SweetAlert2 toast and optional redirect.

## Approvals (Admin)
- [ ] `pages/admin/pending.php` lists Submitted items from all collections (Admin only).
- [ ] `api/approve.php` and `api/reject.php` update status and timestamps; lock edits for non‑Admin.
- [ ] Append audit entries to `logs/audit.log` (ISO time, user id, action, ids).

## DataTables Integration
- [ ] Initialize DataTable in `pages/dashboard.php` with AJAX to `/api/search.php`.
- [ ] Columns: entity, region, zone, group, church, country, period (YYYY‑MM), normalized ESPEES, status.
- [ ] Page length 25; client filters map to query params.

## KPIs & Leaderboards
- [ ] KPI cards computed client‑side from `/api/search.php` summary or server‑side via a small `/api/summary.php` (optional).
- [ ] Top‑N widget fetches `/api/top.php` with `level`, `metric`, `n` (10/25/50/100).

## Security & Integrity
- [ ] Enforce `require_login()` in `public/index.php` for all pages except `login`.
- [ ] Check `is_admin()` in Admin pages and all Admin APIs.
- [ ] CSRF on all POST APIs; reject on failure.
- [ ] Escape outputs in pages; validate/sanitize inputs server‑side.
- [ ] Throttle login attempts; store last attempt in session.
- [ ] Deny direct web access to `/data` and `/logs` via server config.

## QA: RZM Scoping
- [ ] Verify RZM sees only their `region_id` + `zone_id` across `/api/search.php` and `/api/top.php` and dashboard.
- [ ] Verify Admin sees all records and can approve/reject/edit.

## QA: Normalization
- [ ] CURRENCY: `amount * fx_to_local_espees * local_espees_to_global`.
- [ ] ESPEES: `amount * local_espees_to_global`.
- [ ] Per‑program money normalization mirrors totals; `rates_version` set.

## Backups & Ops
- [ ] Add daily cron/CLI to zip `data/` → `backups/data-YYYYMMDD.zip`.
- [ ] Optional admin button to trigger manual backup.

## Performance
- [ ] Collections remain partitioned; consider server‑side pagination in `/api/search.php` if needed.

## Deployment
- [ ] `public/index.php` includes libs and page files; API scripts live under `/api`.
- [ ] Production PHP settings: timezone, error_log on, display_errors off.
- [ ] Web server blocks direct access to `/data` and `/logs`.

## Acceptance Criteria (Done When)
- [ ] Login works for ADMIN and RZM; role guard enforced.
- [ ] CategoryB and Adult forms submit, persist, and compute normalized ESPEES via `/api/submit.php`.
- [ ] `/api/search.php` and `/api/top.php` return correct JSON with scoping, filtering, sorting, and pagination.
- [ ] Dashboard shows KPIs, DataTable, and Top‑N widgets.
- [ ] Admin can edit registries, update rates, and batch recompute normalization via simple admin pages + APIs.
- [ ] Approve/Reject flow updates statuses and writes audit logs.

zones and regions breakdown 
Region 1

BLW Southern Africa Region

SA Zone 2

Durban

International Missions for South East Asia Directorate

CE India

Cape Town Zone 2

SA Zone 1

Middle East Asia

BLW Asia & North Africa Region

SA Zone 5

Cape Town Zone 1

Region 2

Western Europe Zone 1

UK Zone 3 Region 2

Ottawa Zone

UK Zone 4 Region 2

UK Zone 2 Region 1

USA Zone 1 Region 1

USA Zone 2 Region 1

BLW Europe Region

UK Zone 1 Region 1

Western Europe Zone 2

Toronto Zone

BLW USA Region 1

Dallas Zone USA Region 2

Australia Zone

USA Region 3

BLW USA Region 2

BLW Canada Sub-Region

BLW Europe Zone DSP

USA Zone 1 Region 2 / Pacific Islands Region / New Zealand

Quebec Zone

UK Zone 4 Region 1

Western Europe Zone 4

Eastern Europe

CE Amsterdam DSP

Western Europe Zone 3

UK Zone 1 (Region 2)

UK Zone 3 DSP

Region 3

EWCA Zone 4

Lagos Zone 1

North East Zone 1

Kenya Zone

Lagos Sub Zone A

Northern Nigeria Central Zone 2

EWCA Zone 6

Ibadan Zone 1

South West Zone 2

Lagos Zone 4

EWCA Zone 5

Accra Zone

Northern Nigeria Central Zone 1

EWCA Zone 2

EWCA Zone 3

MC Abeokuta

South West Zone 3

CE Chad

Ministry Center Warri

Mid-West Zone

Region 4

CELVZ

South South Zone 1

South-South Zone 2

Lagos Sub Zone B

EWCA Zone 1

Ministry Center Ibadan

South South Zone 3

PH Zone 1

Lagos Zone 3

PH Zone 3

Lagos Zone 2

Ministry Center Abuja

Ministry Center Calabar

Ibadan Zone 2

South West Zone 5

Abuja Zone

PH Zone 2

DSC Sub Zone Warri

Abuja Zone 2

South West Zone 4

Region 5

LoveWorld Church Zone

Aba Zone

BLW Nigeria Region

Edo North & Central

Benin Zone 2

Benin Zone 1

Onitsha Zone

BLW West Africa Region

North West Zone 2

South East Zone 1

Lagos Zone 6

South East Zone 3

Lagos Sub Zone C

BLW East & Central Africa Region

Lagos Zone 5

North West Zone 1