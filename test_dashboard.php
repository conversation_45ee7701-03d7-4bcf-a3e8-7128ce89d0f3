<?php
session_start();

// Simulate admin login for testing
$_SESSION['user'] = [
    'id' => 'u_admin',
    'role' => 'ADMIN',
    'email' => '<EMAIL>'
];

echo "<h1>Dashboard Test</h1>";

// Test search endpoint
echo "<h2>Testing Search Endpoint</h2>";
$search_url = "http://localhost/ippc/?r=search";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $search_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p>Search URL: $search_url</p>";
echo "<p>HTTP Code: $http_code</p>";

if ($http_code == 200) {
    $data = json_decode($response, true);
    echo "<p>✅ Search endpoint working!</p>";
    echo "<p>Total records: " . ($data['total'] ?? 'N/A') . "</p>";
    echo "<p>Records in response: " . count($data['rows'] ?? []) . "</p>";

    if (count($data['rows'] ?? []) > 0) {
        echo "<h3>First Record:</h3>";
        echo "<pre>" . json_encode($data['rows'][0], JSON_PRETTY_PRINT) . "</pre>";
    }
} else {
    echo "<p>❌ Search endpoint failed!</p>";
    echo "<p>Response: " . htmlspecialchars(substr($response, 0, 500)) . "</p>";
}

// Test if we can access the dashboard
echo "<h2>Testing Dashboard Access</h2>";
$dashboard_url = "http://localhost/ippc/?r=dashboard";
$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $dashboard_url);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch2, CURLOPT_COOKIE, 'PHPSESSID=' . session_id());
$response2 = curl_exec($ch2);
$http_code2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

echo "<p>Dashboard URL: $dashboard_url</p>";
echo "<p>HTTP Code: $http_code2</p>";

if ($http_code2 == 200) {
    echo "<p>✅ Dashboard accessible!</p>";
    if (strpos($response2, 'records-table') !== false) {
        echo "<p>✅ Dashboard contains records table</p>";
    } else {
        echo "<p>❌ Dashboard missing records table</p>";
    }
} else {
    echo "<p>❌ Dashboard not accessible!</p>";
    echo "<p>Response: " . htmlspecialchars(substr($response2, 0, 500)) . "</p>";
}
?>
