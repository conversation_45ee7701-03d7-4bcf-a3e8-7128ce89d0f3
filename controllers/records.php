<?php
/**
 * Records Controller
 * Handles CRUD operations, submissions, approvals, and rejections
 */

require_once __DIR__ . '/../app/db.php';
require_once __DIR__ . '/../app/auth.php';
require_once __DIR__ . '/../app/access.php';
require_once __DIR__ . '/../app/normalize.php';
require_once __DIR__ . '/../app/validators.php';

function submit_record($form_data) {
    require_csrf();
    $user = get_current_app_user();
    if (!$user) {
        throw new Exception('Authentication required');
    }
    
    // Sanitize input data
    $data = sanitize_form_data($form_data);
    
    // Build the record structure
    $record = build_record_from_form($data, $user);
    
    // Validate the record
    $validation_errors = validate_submission_data($record);
    if (!empty($validation_errors)) {
        return [
            'success' => false,
            'errors' => $validation_errors
        ];
    }
    
    // Ensure user has permission to create this record
    ensure_record_ownership($record);
    
    // Normalize the record (currency conversion, etc.)
    $lookups = db_read('lookups');
    $record = normalize_record($record, $lookups);
    
    // Set initial status to Approved (no draft feature)
    $record['status'] = [
        'state' => 'Approved',
        'submitted_at' => date('c'),
        'approved_at' => date('c'),
        'rejected_at' => null,
        'approved_by' => 'system',
        'rejected_by' => null,
        'rejection_reason' => null
    ];
    
    // Set audit information
    $record['audit'] = [
        'schema_version' => 'v1',
        'created_by_user_id' => $user['id'],
        'created_at' => date('c'),
        'updated_at' => null,
        'last_updated_by' => null,
        'revision' => 1
    ];
    
    // Generate ID
    $record['id'] = generate_ulid();
    
    // Determine collection based on entity type
    $collection = get_collection_name($record['entity_type']);
    
    try {
        // Save to appropriate collection
        db_append($collection, $record);
        
        // Log the action
        audit_log($user['id'], 'record_created', [
            'record_id' => $record['id'],
            'entity_type' => $record['entity_type'],
            'collection' => $collection
        ]);
        
        return [
            'success' => true,
            'record_id' => $record['id'],
            'message' => 'Record created successfully'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to save record: ' . $e->getMessage()
        ];
    }
}




function build_record_from_form($data, $user) {
    // Auto-determine region and zone for RZM users
    $is_rzm = isset($user['role']) && $user['role'] === 'RZM';
    $region_id = $data['region_id'] ?? null;
    $zone_id = $data['zone_id'] ?? $data['zone'] ?? null;

    if ($is_rzm) {
        // For RZM users, use their assigned region and zone
        $region_id = intval($user['region_id'] ?? 1);
        $zone_id = $user['zone_id'] ?? '';
    }
    
    // Build money structure
    $money = [
        'denomination' => $data['money_denomination'] ?? 'CURRENCY',
        'amount' => floatval($data['money_amount'] ?? 0),
        'currency_code' => $data['currency_code'] ?? null
    ];
    
    // Build country structure
    $country = [
        'code' => $data['country'] ?? null
    ];
    
    // Build measures from program fields
    $measures = [];
    $program_registry = db_read('program_registry');
    foreach ($program_registry['programs'] ?? [] as $program) {
        $key = $program['key'];
        $measures[$key] = floatval($data[$key] ?? 0);
    }
    
    // Base record structure
    $record = [
        'entity_type' => $data['entity_type'] ?? 'CategoryB',
        'region_id' => intval($region_id ?? 1),
        'zone_id' => $zone_id ?? '',
        'group' => $data['group'] ?? $data['group_name'] ?? null,
        'church' => $data['church'] ?? null,
        'country' => $country,
        'money' => $money,
        'measures' => $measures,
        'measures_norm' => [
            'espees' => 0,
            'programs' => []
        ]
    ];
    
    // Add entity-specific fields
    if (isset($data['first_name'])) {
        $record['first_name'] = $data['first_name'];
    }
    if (isset($data['surname'])) {
        $record['surname'] = $data['surname'];
    }
    if (isset($data['email'])) {
        $record['email'] = $data['email'];
    }
    if (isset($data['phone'])) {
        $record['phone'] = $data['phone'];
    }
    if (isset($data['ministry_center_name'])) {
        $record['ministry_center_name'] = $data['ministry_center_name'];
    }
    
    return $record;
}

function find_record_by_id($record_id) {
    $collections = [
        'campaigns', 'category_b', 'category_c', 'cells',
        'partners_adult', 'partners_kids', 'partners_teens', 'partners_external'
    ];
    
    foreach ($collections as $collection) {
        try {
            $record = db_find($collection, $record_id);
            if ($record) {
                return $record;
            }
        } catch (Exception $e) {
            continue;
        }
    }
    
    return null;
}

function get_collection_name($entity_type) {
    $mapping = [
        'Campaigns' => 'campaigns',

        'CategoryB' => 'category_b',
        'CategoryC' => 'category_c',
        'Cell' => 'cells',
        'Adult' => 'partners_adult',
        'Kids' => 'partners_kids',
        'Teens' => 'partners_teens',
        'External' => 'partners_external'
    ];
    
    return $mapping[$entity_type] ?? 'category_b';
}

function update_record($record_id, $form_data) {
    require_csrf();
    $user = get_current_app_user();
    if (!$user) {
        throw new Exception('Authentication required');
    }

    // Find the existing record
    $existing_record = find_record_by_id($record_id);
    if (!$existing_record) {
        throw new Exception('Record not found');
    }

    // Ensure user has permission to update this record
    ensure_record_ownership($existing_record);

    // Sanitize input data
    $data = sanitize_form_data($form_data);

    // Build the updated record structure
    $updated_record = build_record_from_form($data, $user);

    // Preserve original record ID and audit information
    $updated_record['id'] = $record_id;

    // Update audit information
    $updated_record['audit'] = $existing_record['audit'];
    $updated_record['audit']['updated_at'] = date('c');
    $updated_record['audit']['last_updated_by'] = $user['id'];
    $updated_record['audit']['revision'] = ($existing_record['audit']['revision'] ?? 1) + 1;

    // Preserve status information
    $updated_record['status'] = $existing_record['status'];

    // Validate the updated record
    $validation_errors = validate_submission_data($updated_record);
    if (!empty($validation_errors)) {
        return [
            'success' => false,
            'errors' => $validation_errors
        ];
    }

    // Normalize the updated record (currency conversion, etc.)
    $lookups = db_read('lookups');
    $updated_record = normalize_record($updated_record, $lookups);

    // Determine collection based on entity type
    $collection = get_collection_name($updated_record['entity_type']);

    try {
        // Update the record in the appropriate collection
        db_update($collection, $record_id, $updated_record);

        // Log the action
        audit_log($user['id'], 'record_updated', [
            'record_id' => $record_id,
            'entity_type' => $updated_record['entity_type'],
            'collection' => $collection
        ]);

        return [
            'success' => true,
            'record_id' => $record_id,
            'message' => 'Record updated successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to update record: ' . $e->getMessage()
        ];
    }
}

function get_pending_records() {
    require_admin();

    $collections = [
        'campaigns', 'category_b', 'category_c', 'cells',
        'partners_adult', 'partners_kids', 'partners_teens', 'partners_external'
    ];

    $pending_records = [];

    foreach ($collections as $collection) {
        try {
            $records = db_read($collection);
            foreach ($records as $record) {
                if (($record['status']['state'] ?? 'Draft') === 'Submitted') {
                    $pending_records[] = $record;
                }
            }
        } catch (Exception $e) {
            continue;
        }
    }

    // Sort by submission date (newest first)
    usort($pending_records, function($a, $b) {
        $date_a = $a['status']['submitted_at'] ?? '';
        $date_b = $b['status']['submitted_at'] ?? '';
        return $date_b <=> $date_a;
    });

    return $pending_records;
}
