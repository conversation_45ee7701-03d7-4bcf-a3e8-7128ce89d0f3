<?php
/**
 * Admin Controller
 * Handles registry management, settings, and admin operations
 */

require_once __DIR__ . '/../app/db.php';
require_once __DIR__ . '/../app/auth.php';
require_once __DIR__ . '/../app/normalize.php';

function save_registry($registry_type, $data) {
    require_admin();
    require_csrf();
    
    $user = get_current_app_user();
    $allowed_registries = ['program_registry', 'category_registry', 'lookups'];
    
    if (!in_array($registry_type, $allowed_registries)) {
        return [
            'success' => false,
            'error' => 'Invalid registry type'
        ];
    }
    
    try {
        // Add metadata
        $data['last_updated'] = date('c');
        $data['updated_by'] = $user['id'];
        
        // Special handling for lookups - update rates version
        if ($registry_type === 'lookups') {
            $data['rates_version'] = date('Y-m-d');
        }
        
        // Validate the data structure
        $validation_result = validate_registry_data($registry_type, $data);
        if (!$validation_result['valid']) {
            return [
                'success' => false,
                'error' => 'Validation failed: ' . $validation_result['error']
            ];
        }
        
        // Save the registry
        db_write($registry_type, $data);
        
        // Log the action
        audit_log($user['id'], 'registry_updated', [
            'registry_type' => $registry_type,
            'rates_version_updated' => $registry_type === 'lookups'
        ]);
        
        $message = ucfirst(str_replace('_', ' ', $registry_type)) . ' updated successfully';
        if ($registry_type === 'lookups') {
            $message .= '. Rates version updated to ' . $data['rates_version'];
        }
        
        return [
            'success' => true,
            'message' => $message,
            'rates_version_updated' => $registry_type === 'lookups',
            'new_rates_version' => $registry_type === 'lookups' ? $data['rates_version'] : null
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to save registry: ' . $e->getMessage()
        ];
    }
}

function validate_registry_data($registry_type, $data) {
    switch ($registry_type) {
        case 'program_registry':
            return validate_program_registry($data);
        case 'category_registry':
            return validate_category_registry($data);
        case 'lookups':
            return validate_lookups($data);
        default:
            return ['valid' => false, 'error' => 'Unknown registry type'];
    }
}

function validate_program_registry($data) {
    if (!isset($data['programs']) || !is_array($data['programs'])) {
        return ['valid' => false, 'error' => 'Programs array is required'];
    }
    
    foreach ($data['programs'] as $index => $program) {
        if (!isset($program['key']) || empty($program['key'])) {
            return ['valid' => false, 'error' => "Program at index {$index} missing key"];
        }
        
        if (!isset($program['label']) || empty($program['label'])) {
            return ['valid' => false, 'error' => "Program {$program['key']} missing label"];
        }
        
        if (!isset($program['type']) || !in_array($program['type'], ['money', 'count'])) {
            return ['valid' => false, 'error' => "Program {$program['key']} must have type 'money' or 'count'"];
        }
        
        if (!isset($program['enabled']) || !is_bool($program['enabled'])) {
            return ['valid' => false, 'error' => "Program {$program['key']} must have boolean 'enabled' field"];
        }
    }
    
    return ['valid' => true];
}

function validate_category_registry($data) {
    if (!isset($data['categories']) || !is_array($data['categories'])) {
        return ['valid' => false, 'error' => 'Categories array is required'];
    }
    
    foreach ($data['categories'] as $index => $category) {
        if (!isset($category['id']) || empty($category['id'])) {
            return ['valid' => false, 'error' => "Category at index {$index} missing id"];
        }
        
        if (!isset($category['label']) || empty($category['label'])) {
            return ['valid' => false, 'error' => "Category {$category['id']} missing label"];
        }
        
        if (!isset($category['entity_type']) || empty($category['entity_type'])) {
            return ['valid' => false, 'error' => "Category {$category['id']} missing entity_type"];
        }
        
        if (!isset($category['programs_allowed']) || !is_array($category['programs_allowed'])) {
            return ['valid' => false, 'error' => "Category {$category['id']} must have programs_allowed array"];
        }
    }
    
    return ['valid' => true];
}

function validate_lookups($data) {
    if (!isset($data['regions']) || !is_array($data['regions'])) {
        return ['valid' => false, 'error' => 'Regions array is required'];
    }
    
    if (!isset($data['zones']) || !is_array($data['zones'])) {
        return ['valid' => false, 'error' => 'Zones array is required'];
    }
    
    if (!isset($data['countries']) || !is_array($data['countries'])) {
        return ['valid' => false, 'error' => 'Countries array is required'];
    }
    
    foreach ($data['countries'] as $index => $country) {
        $required_fields = ['code', 'name', 'currency_code', 'fx_to_local_espees', 'local_espees_to_global'];
        
        foreach ($required_fields as $field) {
            if (!isset($country[$field])) {
                return ['valid' => false, 'error' => "Country at index {$index} missing {$field}"];
            }
        }
        
        if (!is_numeric($country['fx_to_local_espees']) || $country['fx_to_local_espees'] <= 0) {
            return ['valid' => false, 'error' => "Country {$country['code']} has invalid fx_to_local_espees"];
        }
        
        if (!is_numeric($country['local_espees_to_global']) || $country['local_espees_to_global'] <= 0) {
            return ['valid' => false, 'error' => "Country {$country['code']} has invalid local_espees_to_global"];
        }
    }
    
    return ['valid' => true];
}

function recompute_normalizations() {
    require_admin();
    require_csrf();
    
    $user = get_current_app_user();
    
    try {
        $result = recompute_all_normalizations();
        
        audit_log($user['id'], 'normalizations_recomputed', [
            'total_updated' => $result['total_updated'],
            'collections' => array_keys($result['collections'])
        ]);
        
        return [
            'success' => true,
            'message' => "Recomputation completed. {$result['total_updated']} records updated.",
            'details' => $result
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to recompute normalizations: ' . $e->getMessage()
        ];
    }
}

function get_system_stats() {
    require_admin();
    
    $collections = [
        'campaigns', 'category_b', 'category_c', 'cells',
        'partners_adult', 'partners_kids', 'partners_teens', 'partners_external'
    ];
    
    $stats = [
        'total_records' => 0,
        'records_by_collection' => [],
        'total_espees' => 0,
        'last_submission' => null,
        'active_users' => 0
    ];
    
    foreach ($collections as $collection) {
        try {
            $records = db_read($collection);
            $count = count($records);
            $stats['records_by_collection'][$collection] = $count;
            $stats['total_records'] += $count;
            
            foreach ($records as $record) {
                $stats['total_espees'] += floatval($record['measures_norm']['espees'] ?? 0);
                
                $submitted_at = $record['status']['submitted_at'] ?? null;
                if ($submitted_at && (!$stats['last_submission'] || $submitted_at > $stats['last_submission'])) {
                    $stats['last_submission'] = $submitted_at;
                }
            }
        } catch (Exception $e) {
            $stats['records_by_collection'][$collection] = 0;
        }
    }
    
    // Count active users (users who have created records in the last 30 days)
    try {
        $users = db_read('users');
        $stats['total_users'] = count($users);
    } catch (Exception $e) {
        $stats['total_users'] = 0;
    }
    
    $stats['total_espees'] = round($stats['total_espees'], 2);
    
    return $stats;
}

function create_backup() {
    require_admin();
    
    $user = get_current_app_user();
    $backup_name = 'data-' . date('Ymd-His') . '.zip';
    $backup_path = __DIR__ . '/../backups/' . $backup_name;
    
    // Ensure backup directory exists
    $backup_dir = dirname($backup_path);
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    try {
        $zip = new ZipArchive();
        if ($zip->open($backup_path, ZipArchive::CREATE) !== TRUE) {
            throw new Exception('Cannot create backup file');
        }
        
        // Add all data files to the zip
        $data_dir = __DIR__ . '/../data/';
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($data_dir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'json') {
                $relative_path = str_replace($data_dir, '', $file->getPathname());
                $zip->addFile($file->getPathname(), 'data/' . $relative_path);
            }
        }
        
        $zip->close();
        
        audit_log($user['id'], 'backup_created', [
            'backup_name' => $backup_name,
            'backup_size' => filesize($backup_path)
        ]);
        
        return [
            'success' => true,
            'message' => 'Backup created successfully',
            'backup_name' => $backup_name,
            'backup_size' => filesize($backup_path)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to create backup: ' . $e->getMessage()
        ];
    }
}

// User Management Functions
function create_user($data) {
    require_admin();

    // Validate input
    $errors = validate_user_data($data, false);
    if (!empty($errors)) {
        return [
            'success' => false,
            'errors' => $errors
        ];
    }

    $current_user = get_current_app_user();

    // Generate user ID
    $user_id = generate_ulid();

    // Prepare user data
    $user = [
        'id' => $user_id,
        'name' => trim($data['name']),
        'email' => trim($data['email']),
        'password_hash' => password_hash($data['password'], PASSWORD_DEFAULT),
        'role' => $data['role'],
        'status' => $data['status'] ?? 'active',
        'created_at' => date('c'),
        'created_by' => $current_user['id'],
        'last_login' => null
    ];

    // Add RZM-specific fields
    if ($data['role'] === 'RZM') {
        $user['region_id'] = intval($data['region_id']);
        $user['zone_id'] = trim($data['zone_id']);
    }

    try {
        // Check if email already exists
        $existing_users = db_read('users');
        foreach ($existing_users as $existing) {
            if ($existing['email'] === $user['email']) {
                return [
                    'success' => false,
                    'error' => 'A user with this email address already exists'
                ];
            }
        }

        // Add user to database
        db_append('users', $user);

        // Log the action
        audit_log($current_user['id'], 'user_created', [
            'user_id' => $user_id,
            'email' => $user['email'],
            'role' => $user['role']
        ]);

        return [
            'success' => true,
            'message' => 'User created successfully',
            'user_id' => $user_id
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to create user: ' . $e->getMessage()
        ];
    }
}

function update_user($user_id, $data) {
    require_admin();

    // Get current user data
    $user = db_find('users', $user_id);
    if (!$user) {
        return [
            'success' => false,
            'error' => 'User not found'
        ];
    }

    // Validate input
    $errors = validate_user_data($data, true);
    if (!empty($errors)) {
        return [
            'success' => false,
            'errors' => $errors
        ];
    }

    $current_user = get_current_app_user();

    // Prepare updated user data
    $updated_user = $user;
    $updated_user['name'] = trim($data['name']);
    $updated_user['email'] = trim($data['email']);
    $updated_user['role'] = $data['role'];
    $updated_user['status'] = $data['status'] ?? 'active';

    // Update password if provided
    if (!empty($data['password'])) {
        $updated_user['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
    }

    // Update RZM-specific fields
    if ($data['role'] === 'RZM') {
        $updated_user['region_id'] = intval($data['region_id']);
        $updated_user['zone_id'] = trim($data['zone_id']);
    } else {
        unset($updated_user['region_id']);
        unset($updated_user['zone_id']);
    }

    try {
        // Check if email already exists (excluding current user)
        $existing_users = db_read('users');
        foreach ($existing_users as $existing) {
            if ($existing['id'] !== $user_id && $existing['email'] === $updated_user['email']) {
                return [
                    'success' => false,
                    'error' => 'A user with this email address already exists'
                ];
            }
        }

        // Update user in database
        db_update('users', $user_id, $updated_user);

        // Log the action
        audit_log($current_user['id'], 'user_updated', [
            'user_id' => $user_id,
            'email' => $updated_user['email'],
            'role' => $updated_user['role']
        ]);

        return [
            'success' => true,
            'message' => 'User updated successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to update user: ' . $e->getMessage()
        ];
    }
}

function delete_user($user_id) {
    require_admin();

    // Get user data before deletion
    $user = db_find('users', $user_id);
    if (!$user) {
        return [
            'success' => false,
            'error' => 'User not found'
        ];
    }

    // Don't allow deletion of admin users (except by other admins)
    $current_user = get_current_app_user();
    if ($user['role'] === 'ADMIN' && $current_user['id'] !== $user_id) {
        return [
            'success' => false,
            'error' => 'Cannot delete admin users'
        ];
    }

    try {
        // Delete user from database
        db_delete('users', $user_id);

        // Log the action
        audit_log($current_user['id'], 'user_deleted', [
            'user_id' => $user_id,
            'email' => $user['email'],
            'role' => $user['role']
        ]);

        return [
            'success' => true,
            'message' => 'User deleted successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to delete user: ' . $e->getMessage()
        ];
    }
}

function toggle_user_status($user_id, $status) {
    require_admin();

    // Get user data
    $user = db_find('users', $user_id);
    if (!$user) {
        return [
            'success' => false,
            'error' => 'User not found'
        ];
    }

    $current_user = get_current_app_user();

    // Don't allow deactivation of the current user
    if ($current_user['id'] === $user_id && $status === 'inactive') {
        return [
            'success' => false,
            'error' => 'Cannot deactivate your own account'
        ];
    }

    try {
        // Update user status
        $new_status = $status === 'active' ? 'active' : 'inactive';
        db_update('users', $user_id, ['status' => $new_status]);

        // Log the action
        audit_log($current_user['id'], 'user_status_changed', [
            'user_id' => $user_id,
            'email' => $user['email'],
            'old_status' => $user['status'] ?? 'active',
            'new_status' => $new_status
        ]);

        return [
            'success' => true,
            'message' => 'User status updated successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to update user status: ' . $e->getMessage()
        ];
    }
}

function approve_user($user_id) {
    require_admin();

    // Get user data
    $user = db_find('users', $user_id);
    if (!$user) {
        return [
            'success' => false,
            'error' => 'User not found'
        ];
    }

    // Check if user is already approved
    if (($user['approval_status'] ?? 'pending') === 'approved') {
        return [
            'success' => false,
            'error' => 'User is already approved'
        ];
    }

    $current_user = get_current_app_user();

    try {
        // Update user approval status
        db_update('users', $user_id, ['approval_status' => 'approved']);

        // Log the action
        audit_log($current_user['id'], 'user_approved', [
            'user_id' => $user_id,
            'email' => $user['email'],
            'role' => $user['role']
        ]);

        return [
            'success' => true,
            'message' => 'User approved successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to approve user: ' . $e->getMessage()
        ];
    }
}

function deny_user($user_id) {
    require_admin();

    // Get user data
    $user = db_find('users', $user_id);
    if (!$user) {
        return [
            'success' => false,
            'error' => 'User not found'
        ];
    }

    // Check if user is already denied
    if (($user['approval_status'] ?? 'pending') === 'denied') {
        return [
            'success' => false,
            'error' => 'User is already denied'
        ];
    }

    $current_user = get_current_app_user();

    try {
        // Update user approval status
        db_update('users', $user_id, ['approval_status' => 'denied']);

        // Log the action
        audit_log($current_user['id'], 'user_denied', [
            'user_id' => $user_id,
            'email' => $user['email'],
            'role' => $user['role']
        ]);

        return [
            'success' => true,
            'message' => 'User denied successfully'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Failed to deny user: ' . $e->getMessage()
        ];
    }
}

function validate_user_data($data, $is_edit = false) {
    $errors = [];

    // Name validation
    if (empty(trim($data['name'] ?? ''))) {
        $errors['name'] = 'Name is required';
    } elseif (strlen(trim($data['name'])) < 2) {
        $errors['name'] = 'Name must be at least 2 characters long';
    }

    // Email validation
    if (empty(trim($data['email'] ?? ''))) {
        $errors['email'] = 'Email is required';
    } elseif (!filter_var(trim($data['email']), FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Please enter a valid email address';
    }

    // Password validation
    if (!$is_edit || !empty($data['password'])) {
        if (empty($data['password'])) {
            $errors['password'] = 'Password is required';
        } elseif (strlen($data['password']) < 8) {
            $errors['password'] = 'Password must be at least 8 characters long';
        } elseif ($data['password'] !== ($data['password_confirm'] ?? '')) {
            $errors['password_confirm'] = 'Passwords do not match';
        }
    }

    // Role validation
    if (empty($data['role']) || !in_array($data['role'], ['ADMIN', 'RZM'])) {
        $errors['role'] = 'Please select a valid role';
    }

    // RZM-specific validation
    if (($data['role'] ?? '') === 'RZM') {
        if (empty($data['region_id'])) {
            $errors['region_id'] = 'Region is required for RZM users';
        }
        if (empty(trim($data['zone_id'] ?? ''))) {
            $errors['zone_id'] = 'Zone is required for RZM users';
        }
    }

    return $errors;
}
