<?php
/**
 * Search and Top-N Controllers
 * Handles search API and top entity rankings
 */

require_once __DIR__ . '/../app/db.php';
require_once __DIR__ . '/../app/access.php';
require_once __DIR__ . '/../app/auth.php';

function search($params = []) {
    $collections = [
        'campaigns', 'category_b', 'category_c', 'cells',
        'partners_adult', 'partners_kids', 'partners_teens', 'partners_external'
    ];
    
    // Load all records
    $all_records = [];
    foreach ($collections as $collection) {
        try {
            $records = db_read($collection);
            foreach ($records as $record) {
                $all_records[] = $record;
            }
        } catch (Exception $e) {
            // Skip collections that don't exist or can't be read
            continue;
        }
    }
    
    // Apply scope restrictions
    $all_records = apply_scope($all_records);
    
    // Apply filters
    $filtered_records = array_filter($all_records, function($record) use ($params) {
        return apply_search_filters($record, $params);
    });
    
    // Sort records
    $sorted_records = sort_records($filtered_records, $params);
    
    // Apply pagination
    $page = max(1, intval($params['page'] ?? 1));
    $requested_page_size = intval($params['page_size'] ?? 25);
    $page_size = in_array($requested_page_size, [10, 25, 50, 100]) 
        ? $requested_page_size : 25;
    
    $total = count($sorted_records);
    $offset = ($page - 1) * $page_size;
    $paginated_records = array_slice($sorted_records, $offset, $page_size);
    
    // Ensure page_size is never zero to prevent division by zero
    $safe_page_size = max(1, $page_size);
    
    return [
        'total' => $total,
        'page' => $page,
        'page_size' => $safe_page_size,
        'total_pages' => $total > 0 ? ceil($total / $safe_page_size) : 1,
        'rows' => array_values($paginated_records),
        'summary' => calculate_summary($filtered_records)
    ];
}

function apply_search_filters($record, $params) {
    // Text search
    $query = strtolower(trim($params['q'] ?? ''));
    if ($query) {
        $searchable_text = strtolower(json_encode([
            $record['id'] ?? '',
            $record['entity_type'] ?? '',
            $record['zone_id'] ?? '',
            $record['group'] ?? ($record['group_name'] ?? ''),
            $record['church'] ?? ($record['church_name'] ?? ''),
            $record['first_name'] ?? '',
            $record['surname'] ?? '',
            $record['ministry_center_name'] ?? ''
        ]));
        
        if (strpos($searchable_text, $query) === false) {
            return false;
        }
    }
    
    // Entity types filter
    if (!empty($params['entity_types']) && is_array($params['entity_types'])) {
        if (!in_array($record['entity_type'] ?? '', $params['entity_types'])) {
            return false;
        }
    }
    
    // Region filter
    if (!empty($params['region_ids']) && is_array($params['region_ids'])) {
        if (!in_array($record['region_id'] ?? null, array_map('intval', $params['region_ids']))) {
            return false;
        }
    }
    
    // Zone filter
    if (!empty($params['zone_ids']) && is_array($params['zone_ids'])) {
        if (!in_array($record['zone_id'] ?? '', $params['zone_ids'])) {
            return false;
        }
    }
    
    // Country filter
    if (!empty($params['countries']) && is_array($params['countries'])) {
        if (!in_array($record['country']['code'] ?? '', $params['countries'])) {
            return false;
        }
    }

    // Groups filter
    if (!empty($params['groups']) && is_array($params['groups'])) {
        if (!in_array($record['group'] ?? '', $params['groups'])) {
            return false;
        }
    }
    
    // Period range filter
    if (!empty($params['period_from']) || !empty($params['period_to'])) {
        $record_period = sprintf('%04d-%02d', 
            $record['period']['year'] ?? 0, 
            $record['period']['month'] ?? 0
        );
        
        if (!empty($params['period_from']) && $record_period < $params['period_from']) {
            return false;
        }
        
        if (!empty($params['period_to']) && $record_period > $params['period_to']) {
            return false;
        }
    }
    
    // Status filter - all records are now approved by default
    // No status filtering needed since all records are approved
    
    // Amount range filter (normalized ESPEES)
    $espees = floatval($record['measures_norm']['espees'] ?? 0);
    if (isset($params['min_amount']) && $espees < floatval($params['min_amount'])) {
        return false;
    }
    
    if (isset($params['max_amount']) && $espees > floatval($params['max_amount'])) {
        return false;
    }
    
    return true;
}

function sort_records($records, $params) {
    $sort_by = $params['sort_by'] ?? 'measures_norm.espees';
    $sort_dir = strtolower($params['sort_dir'] ?? 'desc');
    
    usort($records, function($a, $b) use ($sort_by, $sort_dir) {
        $value_a = get_nested_value($a, $sort_by);
        $value_b = get_nested_value($b, $sort_by);
        
        // Convert to comparable types
        if (is_numeric($value_a) && is_numeric($value_b)) {
            $value_a = floatval($value_a);
            $value_b = floatval($value_b);
        } else {
            $value_a = (string)$value_a;
            $value_b = (string)$value_b;
        }
        
        $comparison = $value_a <=> $value_b;
        return $sort_dir === 'asc' ? $comparison : -$comparison;
    });
    
    return $records;
}

function get_nested_value($array, $path) {
    $parts = explode('.', $path);
    $current = $array;
    
    foreach ($parts as $part) {
        if (is_array($current) && isset($current[$part])) {
            $current = $current[$part];
        } else {
            return null;
        }
    }
    
    return $current;
}

function calculate_summary($records) {
    $total_espees = 0;
    $total_records = count($records);
    $entity_counts = [];

    foreach ($records as $record) {
        $total_espees += floatval($record['measures_norm']['espees'] ?? 0);

        $entity_type = $record['entity_type'] ?? 'Unknown';
        $entity_counts[$entity_type] = ($entity_counts[$entity_type] ?? 0) + 1;
    }

    return [
        'total_records' => $total_records,
        'total_espees' => round($total_espees, 2),
        'average_espees' => $total_records > 0 ? round($total_espees / $total_records, 2) : 0,
        'entity_counts' => $entity_counts
    ];
}

function top_entity($params = []) {
    $level = $params['level'] ?? 'zone';
    $metric = $params['metric'] ?? 'espees';
    $n = in_array(intval($params['n'] ?? 10), [10, 25, 50, 100]) ? intval($params['n']) : 10;
    
    // Load all records
    $collections = [
        'campaigns', 'category_b', 'category_c', 'cells',
        'partners_adult', 'partners_kids', 'partners_teens', 'partners_external'
    ];
    
    $all_records = [];
    foreach ($collections as $collection) {
        try {
            $records = db_read($collection);
            foreach ($records as $record) {
                $all_records[] = $record;
            }
        } catch (Exception $e) {
            continue;
        }
    }
    
    // Apply scope restrictions
    $all_records = apply_scope($all_records);
    
    // Apply any additional filters (period, etc.) - all records are approved
    $filtered_records = array_filter($all_records, function($record) use ($params) {
        // All records are now approved by default, no status filtering needed
        return apply_search_filters($record, $params);
    });
    
    // Group by level
    $key_function = get_key_function($level);
    $metric_function = get_metric_function($metric);
    
    $aggregated = [];
    foreach ($filtered_records as $record) {
        $key = $key_function($record);
        if (!$key) continue;
        
        $value = $metric_function($record);
        $aggregated[$key] = ($aggregated[$key] ?? 0) + $value;
    }
    
    // Sort and take top N
    arsort($aggregated);
    $top_results = [];
    $rank = 1;
    
    foreach (array_slice($aggregated, 0, $n, true) as $key => $value) {
        $top_results[] = [
            'rank' => $rank++,
            'key' => $key,
            'value' => round($value, 2),
            'formatted_value' => number_format($value, 2)
        ];
    }
    
    return $top_results;
}

function get_key_function($level) {
    switch ($level) {
        case 'zone':
            return fn($r) => $r['zone_id'] ?? '';
        case 'group':
            return fn($r) => $r['group'] ?? '';
        case 'church':
            return fn($r) => $r['church'] ?? '';
        case 'country':
            return fn($r) => $r['country']['code'] ?? '';
        case 'person':
            return fn($r) => trim(($r['first_name'] ?? '') . ' ' . ($r['surname'] ?? ''));
        case 'ministry_center':
            return fn($r) => $r['ministry_center_name'] ?? '';
        default:
            return fn($r) => $r['zone_id'] ?? '';
    }
}

function get_metric_function($metric) {
    if ($metric === 'espees') {
        return fn($r) => floatval($r['measures_norm']['espees'] ?? 0);
    } elseif (str_starts_with($metric, 'program:')) {
        $program = substr($metric, 8);
        return fn($r) => floatval($r['measures_norm']['programs'][$program] ?? 0);
    } else {
        return fn($r) => 0.0;
    }
}
