# IPPC Awards Reporting Portal

A comprehensive PHP-based reporting portal for IPPC Awards with role-based access control, currency normalization, and administrative features.

## Features

- **Role-Based Access Control**: ADMIN (full access) and RZM (region/zone scoped access)
- **Currency Normalization**: Automatic conversion from local currencies to global ESPEES
- **Dynamic Forms**: Configurable submission forms for different categories
- **Real-time Dashboard**: KPIs, filtering, sorting, and Top-N leaderboards
- **Admin Management**: Registry editing, approval workflows, and system statistics
- **Modern UI**: Tailwind CSS with responsive design and SweetAlert2 notifications

## Technology Stack

- **Backend**: PHP 8+ with JSON file-based storage
- **Frontend**: Tailwind CSS, jQuery, DataTables, SweetAlert2
- **Storage**: Flat JSON files with file locking for thread safety
- **Server**: Apache with mod_rewrite

## Installation

### Prerequisites

- PHP 8.0 or higher
- Apache web server with mod_rewrite enabled
- Write permissions for `data/` and `logs/` directories

### Setup Steps

1. **Clone or extract the application** to your web server directory (e.g., `/Applications/XAMPP/xamppfiles/htdocs/ippc/`)

2. **Set directory permissions**:
   ```bash
   chmod 755 data/ logs/
   chmod 666 data/*.json
   ```

3. **Configure your web server** to point to the `public/` directory as the document root, or ensure the root `.htaccess` file is working to redirect requests to `public/`.

4. **Verify installation** by accessing the application in your browser:
   ```
   http://localhost/ippc/
   ```

## Default Login Credentials

The application comes with pre-configured demo accounts:

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| Admin | admin@ippc | password | Full system access |
| RZM Lagos | lagos@ippc | password | Lagos Zone 1 only |
| RZM Cape Town | capetown@ippc | password | Cape Town Zone 1 only |
| RZM USA | usa@ippc | password | USA Zone 1 Region 1 only |

**Important**: Change these passwords in production by updating the `password_hash` values in `data/users.json`.

## Directory Structure

```
ippc/
├── app/                    # Core application logic
│   ├── auth.php           # Authentication & session management
│   ├── db.php             # JSON database utilities
│   ├── normalize.php      # Currency normalization logic
│   ├── validators.php     # Form validation functions
│   └── access.php         # Role-based access control
├── controllers/           # Business logic controllers
│   ├── search.php         # Search & top-N functionality
│   ├── records.php        # CRUD operations & approvals
│   └── admin.php          # Admin operations
├── views/                 # User interface templates
│   ├── layout.php         # Main layout template
│   ├── components.php     # Reusable UI components
│   └── pages/             # Individual page templates
├── public/                # Web-accessible files
│   ├── index.php          # Main application router
│   ├── .htaccess          # Apache configuration
│   └── assets/            # Static assets (if any)
├── data/                  # JSON data storage
│   ├── users.json         # User accounts
│   ├── *_registry.json    # System registries
│   ├── lookups.json       # Countries & exchange rates
│   ├── *.json             # Data collections
│   └── forms/             # Form configurations
└── logs/                  # Application logs
    └── audit.log          # Audit trail
```

## Usage Guide

### For RZM Users

1. **Login** with your credentials
2. **Submit Data** via the submission forms:
   - Category B - Ministry Centers
   - Partners - Adult
   - Other categories (as configured)
3. **View Dashboard** to see your submissions and statistics
4. **Track Status** of your submissions (Draft → Submitted → Approved/Rejected)

### For Admin Users

1. **Approve/Reject Submissions** in the Pending Approvals section
2. **Manage System Settings**:
   - Edit program registry (add/remove programs)
   - Update category configurations
   - Modify exchange rates and country settings
3. **Monitor System Statistics** and create backups
4. **Recompute Normalizations** when exchange rates change

## Currency Normalization

The system uses a two-step normalization process:

1. **Local Currency → Local ESPEES**: `amount × fx_to_local_espees`
2. **Local ESPEES → Global ESPEES**: `local_espees × local_espees_to_global`

This allows for fair comparison across different countries and currencies.

## API Endpoints

The application provides several API endpoints for AJAX operations:

- `?r=search` - Search and filter records
- `?r=top` - Get top-N rankings
- `?r=submit` - Submit new records
- `?r=approve` - Approve records (admin only)
- `?r=reject` - Reject records (admin only)

## Security Features

- **CSRF Protection**: All forms include CSRF tokens
- **Role-Based Access**: Strict permission checking
- **Data Scoping**: RZM users only see their region/zone data
- **File Locking**: Thread-safe JSON operations
- **Audit Logging**: All actions are logged
- **Input Validation**: Server-side validation for all inputs

## Customization

### Adding New Programs

1. Go to **Admin → Settings & Registries**
2. Select the **Program Registry** tab
3. Click **Add Program** and fill in the details
4. Save the registry

### Adding New Categories

1. Go to **Admin → Settings & Registries**
2. Select the **Category Registry** tab
3. Click **Add Category** and configure allowed programs
4. Create corresponding form configuration in `data/forms/`

### Updating Exchange Rates

1. Go to **Admin → Settings & Registries**
2. Select the **Countries & Rates** tab
3. Update the exchange rate values
4. Save and click **Recompute All Records**

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Ensure `data/` and `logs/` directories are writable
   - Check file ownership and permissions

2. **Login Issues**
   - Verify user credentials in `data/users.json`
   - Check session configuration in PHP

3. **Data Not Saving**
   - Check file permissions on JSON files
   - Verify disk space availability
   - Review error logs

### Debug Mode

To enable debug mode, edit `public/index.php`:

```php
// Change these lines for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Production Deployment

### Security Checklist

1. **Change Default Passwords**: Update all user passwords in `data/users.json`
2. **Disable Debug Mode**: Turn off error display in `public/index.php`
3. **Secure Directories**: Ensure `data/` and `logs/` are not web-accessible
4. **Enable HTTPS**: Use SSL/TLS encryption
5. **Regular Backups**: Set up automated backup procedures
6. **Monitor Logs**: Review `logs/audit.log` regularly

### Performance Optimization

1. **Enable PHP OPcache**: For better performance
2. **Gzip Compression**: Enable in Apache/Nginx
3. **Static Asset Caching**: Configure proper cache headers
4. **Database Partitioning**: Consider splitting large JSON files

## Support

For technical support or feature requests, please refer to the system documentation or contact the development team.

## License

This application is proprietary software developed for IPPC Awards reporting purposes.
