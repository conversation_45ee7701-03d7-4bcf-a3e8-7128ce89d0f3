<?php
/**
 * Login Page
 * Handles user authentication
 */

$page_title = 'Login - IPPC Awards Portal';
$error_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';

    if (login($email, $password)) {
        header('Location: ?r=dashboard');
        exit;
    } else {
        $error_message = 'Invalid email or password, or account not approved';
    }
}

ob_start();
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">
                IPPC Awards Portal
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Sign in to your account
            </p>
        </div>
        
        <?php if ($error_message): ?>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>
        
        <form class="mt-8 space-y-6" method="POST">
            <?= csrf_token_input() ?>
            
            <div class="space-y-4">
                <div>
                    <label for="email" class="form-label">Email Address <span class="text-red-500">*</span></label>
                    <input id="email" name="email" type="email" required 
                           class="form-input" placeholder="Enter your email"
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                </div>
                
                <div>
                    <label for="password" class="form-label">Password <span class="text-red-500">*</span></label>
                    <input id="password" name="password" type="password" required 
                           class="form-input" placeholder="Enter your password">
                </div>
            </div>
            
            <div>
                <button type="submit" class="btn-primary w-full">
                    Sign In
                </button>
            </div>
        </form>
        
        <div class="mt-8 p-6 bg-blue-50 rounded-xl">
            <h3 class="text-sm font-semibold text-blue-900 mb-3">Demo Accounts</h3>
            <div class="space-y-2 text-sm text-blue-800">
                <div><strong>Admin:</strong> admin@ippc / password</div>
                <div><strong>RZM Lagos:</strong> lagos@ippc / password</div>
                <div><strong>RZM Cape Town:</strong> capetown@ippc / password</div>
                <div><strong>RZM USA:</strong> usa@ippc / password</div>
            </div>
        </div>

        <div class="text-center">
            <p class="text-sm text-gray-600">
                Don't have an account?
                <a href="?r=register" class="text-blue-600 hover:text-blue-500 font-medium">
                    Create Account
                </a>
            </p>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
