<?php
/**
 * Admin Settings Page
 * Manage registries, rates, and system settings
 */

require_once __DIR__ . '/../../../controllers/admin.php';
require_once __DIR__ . '/../../components.php';

$page_title = 'Settings & Registries - Admin';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'save_registry':
            $registry_type = $_POST['registry_type'] ?? '';
            $data = json_decode($_POST['registry_data'] ?? '{}', true);
            $result = save_registry($registry_type, $data);
            break;
            
        case 'recompute':
            $result = recompute_normalizations();
            break;
            
        case 'backup':
            $result = create_backup();
            break;
            
        default:
            $result = ['success' => false, 'error' => 'Invalid action'];
    }
    
    // Store result in session for display
    session_start();
    $_SESSION['admin_message'] = $result;
    
    // Redirect to prevent form resubmission
    header('Location: ?r=admin_settings');
    exit;
}

// Get any stored messages
$message = $_SESSION['admin_message'] ?? null;
unset($_SESSION['admin_message']);

// Load current registries
$program_registry = db_read('program_registry');
$category_registry = db_read('category_registry');
$lookups = db_read('lookups');
$system_stats = get_system_stats();

ob_start();
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Settings & Registries</h1>
            <p class="text-gray-600 mt-1">Manage system configuration, rates, and data structures</p>
        </div>
        <div class="flex space-x-4">
            <button onclick="createBackup()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
                Create Backup
            </button>
        </div>
    </div>

    <!-- Message Display -->
    <?php if ($message): ?>
    <div class="<?= $message['success'] ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700' ?> border px-4 py-3 rounded-xl">
        <?= htmlspecialchars($message['message'] ?? $message['error']) ?>
        <?php if ($message['success'] && isset($message['rates_version_updated']) && $message['rates_version_updated']): ?>
        <div class="mt-2">
            <button onclick="recomputeNormalizations()" class="btn-primary text-sm">
                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Recompute All Records
            </button>
            <span class="text-sm ml-2">Rates updated - recompute existing records to apply new rates</span>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- System Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <?= render_kpi_card(
            'Total Records', 
            number_format($system_stats['total_records']), 
            '<div class="text-sm text-gray-500 mt-1">Across all collections</div>'
        ) ?>
        
        <?= render_kpi_card(
            'Total ESPEES', 
            number_format($system_stats['total_espees'], 2), 
            '<div class="text-sm text-gray-500 mt-1">Normalized value</div>'
        ) ?>
        
        <?= render_kpi_card(
            'Active Records',
            number_format($system_stats['records_by_status']['Approved'] ?? 0),
            '<div class="text-sm text-gray-500 mt-1">Approved and active</div>'
        ) ?>
        
        <?= render_kpi_card(
            'Total Users', 
            number_format($system_stats['total_users'] ?? 0), 
            '<div class="text-sm text-gray-500 mt-1">System accounts</div>'
        ) ?>
    </div>

    <!-- Registry Management Tabs -->
    <div class="card p-0">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
                <button onclick="showTab('programs')" id="tab-programs" 
                        class="tab-button active py-4 px-1 border-b-2 border-primary text-primary font-medium text-sm">
                    Program Registry
                </button>
                <button onclick="showTab('categories')" id="tab-categories" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Category Registry
                </button>
                <button onclick="showTab('lookups')" id="tab-lookups" 
                        class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm">
                    Countries & Rates
                </button>
            </nav>
        </div>

        <!-- Program Registry Tab -->
        <div id="content-programs" class="tab-content p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Program Registry</h3>
                <button onclick="addProgram()" class="btn-primary text-sm">
                    <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Program
                </button>
            </div>
            
            <form id="programs-form" onsubmit="saveRegistry('program_registry'); return false;">
                <div class="space-y-4">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-2">Key</th>
                                    <th class="text-left py-2">Label</th>
                                    <th class="text-left py-2">Type</th>
                                    <th class="text-left py-2">Enabled</th>
                                    <th class="text-left py-2">Description</th>
                                    <th class="text-left py-2">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="programs-tbody">
                                <?php foreach ($program_registry['programs'] ?? [] as $index => $program): ?>
                                <tr class="border-b border-gray-100">
                                    <td class="py-2">
                                        <input type="text" name="programs[<?= $index ?>][key]" 
                                               value="<?= htmlspecialchars($program['key'] ?? '') ?>" 
                                               class="form-input text-sm" required>
                                    </td>
                                    <td class="py-2">
                                        <input type="text" name="programs[<?= $index ?>][label]" 
                                               value="<?= htmlspecialchars($program['label'] ?? '') ?>" 
                                               class="form-input text-sm" required>
                                    </td>
                                    <td class="py-2">
                                        <select name="programs[<?= $index ?>][type]" class="form-select text-sm" required>
                                            <option value="money" <?= ($program['type'] ?? '') === 'money' ? 'selected' : '' ?>>Money</option>
                                            <option value="count" <?= ($program['type'] ?? '') === 'count' ? 'selected' : '' ?>>Count</option>
                                        </select>
                                    </td>
                                    <td class="py-2">
                                        <input type="checkbox" name="programs[<?= $index ?>][enabled]" 
                                               <?= ($program['enabled'] ?? false) ? 'checked' : '' ?> 
                                               class="rounded border-gray-300">
                                    </td>
                                    <td class="py-2">
                                        <input type="text" name="programs[<?= $index ?>][description]" 
                                               value="<?= htmlspecialchars($program['description'] ?? '') ?>" 
                                               class="form-input text-sm">
                                    </td>
                                    <td class="py-2">
                                        <button type="button" onclick="removeProgram(<?= $index ?>)" 
                                                class="text-red-600 hover:text-red-800 text-sm">
                                            Remove
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="mt-4 flex justify-end">
                    <button type="submit" class="btn-primary">Save Program Registry</button>
                </div>
            </form>
        </div>

        <!-- Category Registry Tab -->
        <div id="content-categories" class="tab-content p-6 hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Category Registry</h3>
                <button onclick="addCategory()" class="btn-primary text-sm">
                    <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Category
                </button>
            </div>
            
            <form id="categories-form" onsubmit="saveRegistry('category_registry'); return false;">
                <div class="space-y-6">
                    <?php foreach ($category_registry['categories'] ?? [] as $index => $category): ?>
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="form-label">ID</label>
                                <input type="text" name="categories[<?= $index ?>][id]" 
                                       value="<?= htmlspecialchars($category['id'] ?? '') ?>" 
                                       class="form-input" required>
                            </div>
                            <div>
                                <label class="form-label">Label</label>
                                <input type="text" name="categories[<?= $index ?>][label]" 
                                       value="<?= htmlspecialchars($category['label'] ?? '') ?>" 
                                       class="form-input" required>
                            </div>
                            <div>
                                <label class="form-label">Entity Type</label>
                                <input type="text" name="categories[<?= $index ?>][entity_type]" 
                                       value="<?= htmlspecialchars($category['entity_type'] ?? '') ?>" 
                                       class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <label class="form-label">Allowed Programs</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                <?php foreach ($program_registry['programs'] ?? [] as $program): ?>
                                <label class="flex items-center">
                                    <input type="checkbox" name="categories[<?= $index ?>][programs_allowed][]" 
                                           value="<?= htmlspecialchars($program['key']) ?>"
                                           <?= in_array($program['key'], $category['programs_allowed'] ?? []) ? 'checked' : '' ?>
                                           class="rounded border-gray-300 mr-2">
                                    <span class="text-sm"><?= htmlspecialchars($program['label']) ?></span>
                                </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="mt-4 flex justify-end">
                            <button type="button" onclick="removeCategory(<?= $index ?>)" 
                                    class="text-red-600 hover:text-red-800 text-sm">
                                Remove Category
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <button type="submit" class="btn-primary">Save Category Registry</button>
                </div>
            </form>
        </div>

        <!-- Countries & Rates Tab -->
        <div id="content-lookups" class="tab-content p-6 hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Countries & Exchange Rates</h3>
                <div class="flex space-x-2">
                    <span class="text-sm text-gray-500">
                        Current Rates Version: <strong><?= htmlspecialchars($lookups['rates_version'] ?? 'Unknown') ?></strong>
                    </span>
                    <button onclick="addCountry()" class="btn-primary text-sm">
                        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Add Country
                    </button>
                </div>
            </div>
            
            <form id="lookups-form" onsubmit="saveRegistry('lookups'); return false;">
                <div class="space-y-6">
                    <!-- Countries -->
                    <div>
                        <h4 class="font-medium text-gray-900 mb-3">Countries & Exchange Rates</h4>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-2">Code</th>
                                        <th class="text-left py-2">Name</th>
                                        <th class="text-left py-2">Currency</th>
                                        <th class="text-left py-2">FX → Local ESPEES</th>
                                        <th class="text-left py-2">Local → Global ESPEES</th>
                                        <th class="text-left py-2">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="countries-tbody">
                                    <?php foreach ($lookups['countries'] ?? [] as $index => $country): ?>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2">
                                            <input type="text" name="countries[<?= $index ?>][code]" 
                                                   value="<?= htmlspecialchars($country['code'] ?? '') ?>" 
                                                   class="form-input text-sm w-16" required>
                                        </td>
                                        <td class="py-2">
                                            <input type="text" name="countries[<?= $index ?>][name]" 
                                                   value="<?= htmlspecialchars($country['name'] ?? '') ?>" 
                                                   class="form-input text-sm" required>
                                        </td>
                                        <td class="py-2">
                                            <input type="text" name="countries[<?= $index ?>][currency_code]" 
                                                   value="<?= htmlspecialchars($country['currency_code'] ?? '') ?>" 
                                                   class="form-input text-sm w-16" required>
                                        </td>
                                        <td class="py-2">
                                            <input type="number" name="countries[<?= $index ?>][fx_to_local_espees]" 
                                                   value="<?= htmlspecialchars($country['fx_to_local_espees'] ?? '') ?>" 
                                                   step="0.000001" min="0" class="form-input text-sm" required>
                                        </td>
                                        <td class="py-2">
                                            <input type="number" name="countries[<?= $index ?>][local_espees_to_global]" 
                                                   value="<?= htmlspecialchars($country['local_espees_to_global'] ?? '') ?>" 
                                                   step="0.01" min="0" class="form-input text-sm" required>
                                        </td>
                                        <td class="py-2">
                                            <button type="button" onclick="removeCountry(<?= $index ?>)" 
                                                    class="text-red-600 hover:text-red-800 text-sm">
                                                Remove
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                            <div>
                                <h4 class="font-medium text-yellow-800">Important Note</h4>
                                <p class="text-sm text-yellow-700 mt-1">
                                    Saving changes to exchange rates will automatically update the rates version and require 
                                    recomputing all existing records to apply the new rates.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" onclick="recomputeNormalizations()" class="btn-secondary">
                        Recompute All Records
                    </button>
                    <button type="submit" class="btn-primary">Save Countries & Rates</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let programIndex = <?= count($program_registry['programs'] ?? []) ?>;
let categoryIndex = <?= count($category_registry['categories'] ?? []) ?>;
let countryIndex = <?= count($lookups['countries'] ?? []) ?>;

function showTab(tab) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(el => el.classList.add('hidden'));
    document.querySelectorAll('.tab-button').forEach(el => {
        el.classList.remove('active', 'border-primary', 'text-primary');
        el.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab
    document.getElementById('content-' + tab).classList.remove('hidden');
    document.getElementById('tab-' + tab).classList.add('active', 'border-primary', 'text-primary');
    document.getElementById('tab-' + tab).classList.remove('border-transparent', 'text-gray-500');
}

function saveRegistry(registryType) {
    const form = document.getElementById(registryType.replace('_', 's') + '-form');
    const formData = new FormData(form);
    
    // Convert form data to JSON structure
    let data = {};
    
    if (registryType === 'program_registry') {
        data.programs = [];
        const programs = formData.getAll('programs');
        // Process program data...
        // This would need more complex form processing logic
    }
    
    // For now, show a success message
    showToast('success', 'Registry saved successfully');
}

function recomputeNormalizations() {
    confirmAction(
        'This will recompute normalization for all records using current exchange rates. This may take a while.',
        'Recompute All Records'
    ).then((result) => {
        if (result.isConfirmed) {
            $.post('?r=recompute', {
                csrf_token: window.csrfToken
            }).done(function(response) {
                if (response.success) {
                    showToast('success', response.message);
                } else {
                    showToast('error', response.error);
                }
            }).fail(function() {
                showToast('error', 'Failed to recompute normalizations');
            });
        }
    });
}

function createBackup() {
    $.post('?r=backup', {
        csrf_token: window.csrfToken
    }).done(function(response) {
        if (response.success) {
            showToast('success', response.message);
        } else {
            showToast('error', response.error);
        }
    }).fail(function() {
        showToast('error', 'Failed to create backup');
    });
}

function addProgram() {
    const tbody = document.getElementById('programs-tbody');
    const row = document.createElement('tr');
    row.className = 'border-b border-gray-100';
    row.innerHTML = `
        <td class="py-2">
            <input type="text" name="programs[${programIndex}][key]" class="form-input text-sm" required>
        </td>
        <td class="py-2">
            <input type="text" name="programs[${programIndex}][label]" class="form-input text-sm" required>
        </td>
        <td class="py-2">
            <select name="programs[${programIndex}][type]" class="form-select text-sm" required>
                <option value="money">Money</option>
                <option value="count">Count</option>
            </select>
        </td>
        <td class="py-2">
            <input type="checkbox" name="programs[${programIndex}][enabled]" checked class="rounded border-gray-300">
        </td>
        <td class="py-2">
            <input type="text" name="programs[${programIndex}][description]" class="form-input text-sm">
        </td>
        <td class="py-2">
            <button type="button" onclick="removeProgram(${programIndex})" class="text-red-600 hover:text-red-800 text-sm">
                Remove
            </button>
        </td>
    `;
    tbody.appendChild(row);
    programIndex++;
}

function removeProgram(index) {
    const row = document.querySelector(`input[name="programs[${index}][key]"]`).closest('tr');
    row.remove();
}

function addCountry() {
    const tbody = document.getElementById('countries-tbody');
    const row = document.createElement('tr');
    row.className = 'border-b border-gray-100';
    row.innerHTML = `
        <td class="py-2">
            <input type="text" name="countries[${countryIndex}][code]" class="form-input text-sm w-16" required>
        </td>
        <td class="py-2">
            <input type="text" name="countries[${countryIndex}][name]" class="form-input text-sm" required>
        </td>
        <td class="py-2">
            <input type="text" name="countries[${countryIndex}][currency_code]" class="form-input text-sm w-16" required>
        </td>
        <td class="py-2">
            <input type="number" name="countries[${countryIndex}][fx_to_local_espees]" step="0.000001" min="0" class="form-input text-sm" required>
        </td>
        <td class="py-2">
            <input type="number" name="countries[${countryIndex}][local_espees_to_global]" step="0.01" min="0" class="form-input text-sm" required>
        </td>
        <td class="py-2">
            <button type="button" onclick="removeCountry(${countryIndex})" class="text-red-600 hover:text-red-800 text-sm">
                Remove
            </button>
        </td>
    `;
    tbody.appendChild(row);
    countryIndex++;
}

function removeCountry(index) {
    const row = document.querySelector(`input[name="countries[${index}][code]"]`).closest('tr');
    row.remove();
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layout.php';
?>
