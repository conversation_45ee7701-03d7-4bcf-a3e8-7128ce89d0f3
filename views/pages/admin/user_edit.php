<?php
/**
 * Admin User Edit/Create Page
 * Form for creating new users or editing existing ones
 */

require_once __DIR__ . '/../../../controllers/admin.php';
require_once __DIR__ . '/../../components.php';

$user_id = $_GET['id'] ?? null;
$is_edit = !empty($user_id);

// Load user data if editing
$user = null;
if ($is_edit) {
    $user = db_find('users', $user_id);
    if (!$user) {
        header('Location: ?r=admin_users&error=' . urlencode('User not found'));
        exit;
    }
}

$page_title = $is_edit ? 'Edit User - Admin' : 'Add User - Admin';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_csrf();

    $result = $is_edit ? update_user($user_id, $_POST) : create_user($_POST);

    if ($result['success']) {
        header('Location: ?r=admin_users&success=' . urlencode($result['message']));
        exit;
    } else {
        $errors = $result['errors'] ?? [];
        $error_message = $result['error'] ?? ($is_edit ? 'Failed to update user' : 'Failed to create user');
    }
}

ob_start();
?>

<div class="max-w-2xl mx-auto space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">
                <?= $is_edit ? 'Edit User' : 'Add New User' ?>
            </h1>
            <p class="text-gray-600 mt-1">
                <?= $is_edit ? 'Update user information and permissions' : 'Create a new user account' ?>
            </p>
        </div>
        <a href="?r=admin_users" class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Users
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($error_message) ?>
        <?php if (!empty($errors)): ?>
        <ul class="mt-2 list-disc list-inside">
            <?php foreach ($errors as $field => $error): ?>
            <li><strong><?= htmlspecialchars($field) ?>:</strong> <?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- User Form -->
    <form method="POST" class="space-y-6" id="user-form">
        <?= csrf_token_input() ?>

        <!-- Basic Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?= render_text_input('name', 'Full Name', $user['name'] ?? $_POST['name'] ?? '', true, 'Enter user\'s full name') ?>

                <?= render_text_input('email', 'Email Address', $user['email'] ?? $_POST['email'] ?? '', true, 'Enter email address', 'email') ?>

                <div>
                    <label for="password" class="form-label">
                        Password <?= $is_edit ? '(Leave blank to keep current)' : '(Required)' ?>
                    </label>
                    <input type="password" id="password" name="password"
                           class="form-input" placeholder="Enter password"
                           <?= !$is_edit ? 'required' : '' ?>>
                    <p class="mt-1 text-sm text-gray-500">
                        Password must be at least 8 characters long
                    </p>
                </div>

                <div>
                    <label for="password_confirm" class="form-label">
                        Confirm Password <?= $is_edit ? '(Leave blank to keep current)' : '(Required)' ?>
                    </label>
                    <input type="password" id="password_confirm" name="password_confirm"
                           class="form-input" placeholder="Confirm password"
                           <?= !$is_edit ? 'required' : '' ?>>
                </div>
            </div>
        </div>

        <!-- Role & Permissions -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Role & Permissions</h3>
            <div class="space-y-4">
                <div>
                    <label class="form-label">User Role</label>
                    <div class="mt-2 space-y-2">
                        <div class="flex items-center">
                            <input type="radio" id="role_admin" name="role" value="ADMIN"
                                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                   <?= ($user['role'] ?? $_POST['role'] ?? '') === 'ADMIN' ? 'checked' : '' ?>>
                            <label for="role_admin" class="ml-2 text-sm text-gray-700">
                                <span class="font-medium">Administrator</span>
                                <span class="block text-xs text-gray-500">Full system access, can manage users and settings</span>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="role_rzm" name="role" value="RZM"
                                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                   <?= ($user['role'] ?? $_POST['role'] ?? 'RZM') === 'RZM' ? 'checked' : '' ?>>
                            <label for="role_rzm" class="ml-2 text-sm text-gray-700">
                                <span class="font-medium">Regional Zone Manager</span>
                                <span class="block text-xs text-gray-500">Limited to their assigned region and zone</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- RZM-specific fields -->
                <div id="rzm-fields" class="space-y-4 <?= ($user['role'] ?? $_POST['role'] ?? 'RZM') === 'ADMIN' ? 'hidden' : '' ?>">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?= render_select_input('region_id', 'Region', get_lookup_options('regions'),
                            $user['region_id'] ?? $_POST['region_id'] ?? '', false) ?>

                        <?= render_select_input('zone_id', 'Zone', get_lookup_options('zones'),
                            $user['zone_id'] ?? $_POST['zone_id'] ?? '', false) ?>
                    </div>
                    <p class="text-sm text-gray-600">
                        RZM users will only see data from their assigned region and zone.
                    </p>
                </div>
            </div>
        </div>

        <!-- Account Settings -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Settings</h3>
            <div class="space-y-4">
                <div>
                    <label for="status" class="form-label">Account Status</label>
                    <select id="status" name="status" class="form-select">
                        <option value="active" <?= ($user['status'] ?? $_POST['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>
                            Active - User can log in and use the system
                        </option>
                        <option value="inactive" <?= ($user['status'] ?? $_POST['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>
                            Inactive - User cannot log in
                        </option>
                    </select>
                </div>

                <?php if ($is_edit): ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 mb-2">User Information</h4>
                    <div class="text-sm text-blue-800 space-y-1">
                        <div><strong>User ID:</strong> <?= htmlspecialchars($user['id']) ?></div>
                        <div><strong>Created:</strong> <?= date('M j, Y H:i', strtotime($user['created_at'] ?? '')) ?></div>
                        <div><strong>Last Login:</strong> <?= $user['last_login'] ?? 'Never' ?></div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Submit Actions -->
        <div class="card p-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    <p>
                        <?php if ($is_edit): ?>
                        Changes will take effect immediately. The user will need to log out and back in to see role changes.
                        <?php else: ?>
                        The user will be able to log in immediately after creation.
                        <?php endif; ?>
                    </p>
                </div>
                <div class="flex space-x-4">
                    <a href="?r=admin_users" class="btn-secondary">Cancel</a>
                    <button type="submit" class="btn-primary">
                        <?= $is_edit ? 'Update User' : 'Create User' ?>
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Show/hide RZM fields based on role selection
    $('input[name="role"]').on('change', function() {
        if ($(this).val() === 'RZM') {
            $('#rzm-fields').slideDown();
            $('#rzm-fields select').prop('required', true);
        } else {
            $('#rzm-fields').slideUp();
            $('#rzm-fields select').prop('required', false);
        }
    });

    // Password confirmation validation
    $('#password_confirm').on('blur', function() {
        const password = $('#password').val();
        const confirm = $(this).val();

        if (password && confirm && password !== confirm) {
            $(this).addClass('border-red-500');
            showToast('error', 'Passwords do not match');
        } else {
            $(this).removeClass('border-red-500');
            if (password && confirm) {
                $(this).addClass('border-green-500');
            }
        }
    });

    // Password strength validation
    $('#password').on('blur', function() {
        const password = $(this).val();
        if (password && password.length < 8) {
            $(this).addClass('border-red-500');
            showToast('error', 'Password must be at least 8 characters long');
        } else if (password) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        }
    });

    // Form validation
    $('#user-form').on('submit', function(e) {
        let isValid = true;

        // Required field validation
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('border-red-500');
            } else {
                $(this).removeClass('border-red-500');
            }
        });

        // Email validation
        const email = $('#email').val();
        if (email && !isValidEmail(email)) {
            isValid = false;
            $('#email').addClass('border-red-500');
            showToast('error', 'Please enter a valid email address');
        }

        // Password validation for new users
        <?php if (!$is_edit): ?>
        const password = $('#password').val();
        const confirm = $('#password_confirm').val();

        if (!password || password.length < 8) {
            isValid = false;
            $('#password').addClass('border-red-500');
            showToast('error', 'Password must be at least 8 characters long');
        }

        if (password !== confirm) {
            isValid = false;
            $('#password_confirm').addClass('border-red-500');
            showToast('error', 'Passwords do not match');
        }
        <?php endif; ?>

        // Password validation for edits (if password is provided)
        <?php if ($is_edit): ?>
        const password = $('#password').val();
        const confirm = $('#password_confirm').val();

        if (password && password.length < 8) {
            isValid = false;
            $('#password').addClass('border-red-500');
            showToast('error', 'Password must be at least 8 characters long');
        }

        if (password && password !== confirm) {
            isValid = false;
            $('#password_confirm').addClass('border-red-500');
            showToast('error', 'Passwords do not match');
        }
        <?php endif; ?>

        if (!isValid) {
            e.preventDefault();
        }
    });
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layout.php';
?>
