<?php
/**
 * Admin User Management Page
 * Lists all users with CRUD operations
 */

require_once __DIR__ . '/../../../controllers/admin.php';
require_once __DIR__ . '/../../components.php';

$page_title = 'User Management - Admin';

// Get all users
$users = db_read('users');
$user_count = count($users);

// Get user statistics
$admin_count = count(array_filter($users, fn($u) => ($u['role'] ?? '') === 'ADMIN'));
$rzm_count = count(array_filter($users, fn($u) => ($u['role'] ?? '') === 'RZM'));

// Handle delete action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    require_csrf();

    switch ($_POST['action']) {
        case 'delete_user':
            $user_id = $_POST['user_id'] ?? '';
            if ($user_id) {
                // Don't allow deletion of the current user
                $current_user = get_current_app_user();
                if ($current_user['id'] !== $user_id) {
                    $result = delete_user($user_id);
                    if ($result['success']) {
                        header('Location: ?r=admin_users&success=' . urlencode($result['message']));
                    } else {
                        header('Location: ?r=admin_users&error=' . urlencode($result['error']));
                    }
                    exit;
                }
            }
            break;

        case 'toggle_status':
            $user_id = $_POST['user_id'] ?? '';
            $status = $_POST['status'] ?? 'active';
            if ($user_id) {
                $result = toggle_user_status($user_id, $status);
                if ($result['success']) {
                    header('Location: ?r=admin_users&success=' . urlencode($result['message']));
                } else {
                    header('Location: ?r=admin_users&error=' . urlencode($result['error']));
                }
                exit;
            }
            break;

        case 'approve_user':
            $user_id = $_POST['user_id'] ?? '';
            if ($user_id) {
                $result = approve_user($user_id);
                if ($result['success']) {
                    header('Location: ?r=admin_users&success=' . urlencode($result['message']));
                } else {
                    header('Location: ?r=admin_users&error=' . urlencode($result['error']));
                }
                exit;
            }
            break;

        case 'deny_user':
            $user_id = $_POST['user_id'] ?? '';
            if ($user_id) {
                $result = deny_user($user_id);
                if ($result['success']) {
                    header('Location: ?r=admin_users&success=' . urlencode($result['message']));
                } else {
                    header('Location: ?r=admin_users&error=' . urlencode($result['error']));
                }
                exit;
            }
            break;
    }
}

// Get success/error messages
$success_message = $_GET['success'] ?? null;
$error_message = $_GET['error'] ?? null;

ob_start();
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
            <p class="text-gray-600 mt-1">Manage system users and their access permissions</p>
        </div>
        <div class="flex space-x-4">
            <button onclick="refreshUsers()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
            <a href="?r=admin_user_edit" class="btn-primary">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add User
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($success_message) ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <!-- User Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <?= render_kpi_card(
            'Total Users',
            number_format($user_count),
            '<div class="text-sm text-gray-500 mt-1">Registered accounts</div>'
        ) ?>

        <?= render_kpi_card(
            'Admin Users',
            number_format($admin_count),
            '<div class="text-sm text-gray-500 mt-1">Full system access</div>'
        ) ?>

        <?= render_kpi_card(
            'RZM Users',
            number_format($rzm_count),
            '<div class="text-sm text-gray-500 mt-1">Regional access</div>'
        ) ?>

        <?php
        $recent_users = count(array_filter($users, function($u) {
            return isset($u['created_at']) &&
                   strtotime($u['created_at']) > strtotime('-30 days');
        }));
        ?>
        <?= render_kpi_card(
            'Recent Users',
            number_format($recent_users),
            '<div class="text-sm text-gray-500 mt-1">Added in last 30 days</div>'
        ) ?>
    </div>

    <!-- Users Table -->
    <div class="card p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Users</h3>
            <div class="flex space-x-2">
                <input type="text" id="user-search" placeholder="Search users..."
                       class="form-input w-64 text-sm">
                <select id="role-filter" class="form-select w-32 text-sm">
                    <option value="">All Roles</option>
                    <option value="ADMIN">Admin</option>
                    <option value="RZM">RZM</option>
                </select>
                <select id="approval-filter" class="form-select w-40 text-sm">
                    <option value="">All Approval Status</option>
                    <option value="approved">Approved</option>
                    <option value="pending">Pending</option>
                    <option value="denied">Denied</option>
                </select>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table id="users-table" class="w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4">User</th>
                        <th class="text-left py-3 px-4">Role</th>
                        <th class="text-left py-3 px-4">Location</th>
                        <th class="text-left py-3 px-4">Status</th>
                        <th class="text-left py-3 px-4">Approval</th>
                        <th class="text-left py-3 px-4">Created</th>
                        <th class="text-left py-3 px-4">Last Login</th>
                        <th class="text-left py-3 px-4">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    usort($users, function($a, $b) {
                        return strtotime($b['created_at'] ?? '2020-01-01') <=> strtotime($a['created_at'] ?? '2020-01-01');
                    });

                    foreach ($users as $user):
                        $is_current_user = ($user['id'] ?? '') === (get_current_app_user()['id'] ?? '');
                        $status = $user['status'] ?? 'active';
                        $status_class = $status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                        $approval_status = $user['approval_status'] ?? 'approved';
                        $approval_class = match($approval_status) {
                            'approved' => 'bg-green-100 text-green-800',
                            'pending' => 'bg-yellow-100 text-yellow-800',
                            'denied' => 'bg-red-100 text-red-800',
                            default => 'bg-gray-100 text-gray-800'
                        };
                    ?>
                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                        <td class="py-4 px-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">
                                            <?= htmlspecialchars(substr($user['name'] ?? $user['email'], 0, 2)) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <div class="font-medium text-gray-900">
                                        <?= htmlspecialchars($user['name'] ?? 'N/A') ?>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <?= htmlspecialchars($user['email'] ?? '') ?>
                                        <?php if ($is_current_user): ?>
                                        <span class="ml-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">You</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                <?= ($user['role'] ?? '') === 'ADMIN' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800' ?>">
                                <?= htmlspecialchars($user['role'] ?? 'Unknown') ?>
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <div class="text-sm text-gray-900">
                                <?php if (($user['role'] ?? '') === 'RZM'): ?>
                                Region <?= htmlspecialchars($user['region_id'] ?? 'N/A') ?>
                                <div class="text-xs text-gray-500">
                                    <?= htmlspecialchars($user['zone_id'] ?? '') ?>
                                </div>
                                <?php else: ?>
                                <span class="text-gray-500">N/A</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 text-xs font-medium rounded-full <?= $status_class ?>">
                                <?= ucfirst($status) ?>
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 text-xs font-medium rounded-full <?= $approval_class ?>">
                                <?= ucfirst($approval_status) ?>
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <div class="text-sm text-gray-900">
                                <?= date('M j, Y', strtotime($user['created_at'] ?? '')) ?>
                            </div>
                            <div class="text-xs text-gray-500">
                                <?= date('H:i', strtotime($user['created_at'] ?? '')) ?>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <div class="text-sm text-gray-900">
                                <?= $user['last_login'] ?? 'Never' ?>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex space-x-2">
                                <a href="?r=admin_user_edit&id=<?= htmlspecialchars($user['id'] ?? '') ?>"
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    Edit
                                </a>

                                <?php if (!$is_current_user && $approval_status === 'pending'): ?>
                                <form method="POST" class="inline" onsubmit="return confirmAction('Are you sure you want to approve this user?')">
                                    <?= csrf_token_input() ?>
                                    <input type="hidden" name="action" value="approve_user">
                                    <input type="hidden" name="user_id" value="<?= htmlspecialchars($user['id'] ?? '') ?>">
                                    <button type="submit" class="text-green-600 hover:text-green-800 text-sm">
                                        Approve
                                    </button>
                                </form>

                                <form method="POST" class="inline" onsubmit="return confirmAction('Are you sure you want to deny this user?')">
                                    <?= csrf_token_input() ?>
                                    <input type="hidden" name="action" value="deny_user">
                                    <input type="hidden" name="user_id" value="<?= htmlspecialchars($user['id'] ?? '') ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                        Deny
                                    </button>
                                </form>
                                <?php endif; ?>

                                <?php if (!$is_current_user && $approval_status !== 'pending'): ?>
                                <form method="POST" class="inline" onsubmit="return confirmAction('Are you sure you want to <?= $status === 'active' ? 'deactivate' : 'activate' ?> this user?')">
                                    <?= csrf_token_input() ?>
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="user_id" value="<?= htmlspecialchars($user['id'] ?? '') ?>">
                                    <input type="hidden" name="status" value="<?= $status === 'active' ? 'inactive' : 'active' ?>">
                                    <button type="submit" class="text-<?= $status === 'active' ? 'red' : 'green' ?>-600 hover:text-<?= $status === 'active' ? 'red' : 'green' ?>-800 text-sm">
                                        <?= $status === 'active' ? 'Deactivate' : 'Activate' ?>
                                    </button>
                                </form>

                                <form method="POST" class="inline" onsubmit="return confirmAction('Are you sure you want to delete this user? This action cannot be undone.')">
                                    <?= csrf_token_input() ?>
                                    <input type="hidden" name="action" value="delete_user">
                                    <input type="hidden" name="user_id" value="<?= htmlspecialchars($user['id'] ?? '') ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                        Delete
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Search functionality
    $('#user-search').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('#users-table tbody tr').each(function() {
            const text = $(this).text().toLowerCase();
            $(this).toggle(text.includes(searchTerm));
        });
    });

    // Role filter
    $('#role-filter').on('change', function() {
        const roleFilter = $(this).val();
        $('#users-table tbody tr').each(function() {
            if (!roleFilter) {
                $(this).show();
                return;
            }

            const role = $(this).find('td:nth-child(2) span').text().trim();
            $(this).toggle(role === roleFilter);
        });
    });

    // Approval filter
    $('#approval-filter').on('change', function() {
        const approvalFilter = $(this).val();
        $('#users-table tbody tr').each(function() {
            if (!approvalFilter) {
                $(this).show();
                return;
            }

            const approval = $(this).find('td:nth-child(5) span').text().trim().toLowerCase();
            $(this).toggle(approval === approvalFilter);
        });
    });
});

function refreshUsers() {
    window.location.reload();
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layout.php';
?>
