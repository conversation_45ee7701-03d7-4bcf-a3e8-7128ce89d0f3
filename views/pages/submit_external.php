<?php
/**
 * Partners - External Submission Form
 * External organization partnerships submission form
 */

require_once __DIR__ . '/../../app/db.php';
require_once __DIR__ . '/../../controllers/records.php';
require_once __DIR__ . '/../components.php';

$page_title = 'Submit Partners - External';

// Load form configuration
$form_config = db_read('forms/partners_external');
$program_registry = db_read('program_registry');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = submit_record($_POST);

    if ($result['success']) {
        header('Location: ?r=dashboard&success=' . urlencode($result['message']));
        exit;
    } else {
        $errors = $result['errors'] ?? [];
        $error_message = $result['error'] ?? 'Submission failed';
    }
}

ob_start();
?>

<div class="max-w-4xl mx-auto space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Partners - External</h1>
            <p class="text-gray-600 mt-1">Submit data for external organization partnerships and contributions</p>
        </div>
        <a href="?r=dashboard" class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($error_message) ?>
        <?php if (!empty($errors)): ?>
        <ul class="mt-2 list-disc list-inside">
            <?php foreach ($errors as $field => $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Main Form -->
    <form method="POST" class="space-y-6" id="external-form">
        <?= csrf_token_input() ?>
        <input type="hidden" name="entity_type" value="External">

        <!-- Organization Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Organization Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php
                $organization_fields = ['organization_name', 'contact_person', 'contact_email', 'contact_phone', 'organization_type'];

                foreach ($form_config['fields'] as $field):
                    if (in_array($field['name'], $organization_fields)):
                        if ($field['type'] === 'select'):
                            // Handle select fields with inline options (array) vs lookup options (string)
                            if (is_array($field['options'] ?? null)) {
                                // Inline options like organization_type
                                $options = [];
                                foreach ($field['options'] as $option) {
                                    if (is_string($option)) {
                                        $options[$option] = $option;
                                    }
                                }
                                echo render_select_input($field['name'], $field['label'], $options, $_POST[$field['name']] ?? '', $field['required'] ?? false);
                            } else {
                                // Lookup options like 'groups', 'countries'
                                echo render_select_input($field['name'], $field['label'], get_lookup_options($field['options']), $_POST[$field['name']] ?? '', $field['required'] ?? false);
                            }
                        elseif ($field['type'] === 'text' || $field['type'] === 'tel' || $field['type'] === 'email'):
                            $input_type = $field['type'];
                            echo render_text_input($field['name'], $field['label'], $_POST[$field['name']] ?? '', $field['required'] ?? false, $field['placeholder'] ?? '', $input_type);
                        endif;
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Partnership Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Partnership Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php
                $current_user = get_current_app_user();
                $is_admin = is_admin();
                $is_rzm = isset($current_user['role']) && $current_user['role'] === 'RZM';

                $location_fields = ['group', 'country'];

                foreach ($form_config['fields'] as $field):
                    if (in_array($field['name'], $location_fields)):
                        if ($field['type'] === 'select'):
                            if ($field['options'] === 'groups'):
                                // Handle group field - show for ALL users but filter for RZM
                                $selected_value = $_POST[$field['name']] ?? '';
                                ?>
                                <div class="mb-4">
                                    <label for="<?= $field['name'] ?>" class="form-label">
                                        <?= htmlspecialchars($field['label']) ?>
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <select id="<?= $field['name'] ?>" name="<?= $field['name'] ?>" class="form-select" required>
                                        <option value="">Select Group *</option>
                                        <?php foreach (get_lookup_options($field['options']) as $value => $text): ?>
                                            <option value="<?= htmlspecialchars($value) ?>" <?= $value == $selected_value ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($text) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php
                            else:
                                ?>
                                <?= render_select_input($field['name'], $field['label'], get_lookup_options($field['options']), $_POST[$field['name']] ?? '', $field['required'] ?? false) ?>
                                <?php
                            endif;
                        endif;
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Financial Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <?php
                $financial_fields = ['money_denomination', 'money_amount', 'currency_code'];

                foreach ($form_config['fields'] as $field):
                    if (in_array($field['name'], $financial_fields)):
                        if ($field['type'] === 'radio'):
                            $radio_options = [];
                            if (isset($field['options']) && is_array($field['options'])) {
                                foreach ($field['options'] as $option) {
                                    $radio_options[] = [
                                        'value' => $option['value'],
                                        'label' => $option['label']
                                    ];
                                }
                            }
                            echo render_radio_group($field['name'], $field['label'], $radio_options, $_POST[$field['name']] ?? $field['default'] ?? '', $field['required'] ?? false);
                        elseif ($field['type'] === 'number'):
                            echo render_number_input($field['name'], $field['label'], $_POST[$field['name']] ?? 0, $field['required'] ?? false, $field['min'] ?? 0, $field['step'] ?? '0.01', $field['placeholder'] ?? '0.00');
                        elseif ($field['type'] === 'select' && isset($field['depends_on'])):
                            $show_class = (isset($_POST[$field['depends_on']]) && $_POST[$field['depends_on']] === $field['show_when']) ? '' : 'hidden';
                            ?>
                            <div id="currency-field" class="<?= $show_class ?>">
                                <?= render_select_input($field['name'], $field['label'], get_lookup_options($field['options']), $_POST[$field['name']] ?? '', $field['required'] ?? false) ?>
                            </div>
                            <?php
                        endif;
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Program Contributions -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Program Contributions</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php
                $category_registry = db_read('category_registry');
                $partners_external = null;
                foreach ($category_registry['categories'] as $cat) {
                    if ($cat['id'] === 'partners_external') {
                        $partners_external = $cat;
                        break;
                    }
                }

                $allowed_programs = $partners_external['programs_allowed'] ?? [];
                foreach ($program_registry['programs'] as $program):
                    if (!in_array($program['key'], $allowed_programs)) continue;

                    $step = $program['type'] === 'money' ? '0.01' : '1';
                    $placeholder = $program['type'] === 'money' ? '0.00' : '0';
                ?>
                    <?= render_number_input(
                        $program['key'],
                        $program['label'] . ($program['type'] === 'count' ? ' (Count)' : ''),
                        $_POST[$program['key']] ?? 0,
                        false,
                        0,
                        $step,
                        $placeholder
                    ) ?>
                <?php endforeach; ?>
            </div>

            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">Program Types</h4>
                <div class="text-sm text-blue-800 space-y-1">
                    <div><strong>Money Programs:</strong> Enter monetary amounts that will be normalized to global ESPEES</div>
                    <div><strong>Total Amount Received:</strong> Total monetary contributions received from this external partner</div>
                    <div><strong>Grand Total Given:</strong> Grand total of all contributions given by this external partner</div>
                </div>
            </div>
        </div>



        <!-- Submit Actions -->
        <div class="card p-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    <p>Your submission will be saved and approved immediately.</p>
                </div>
                <div class="flex space-x-4">
                    <button type="submit" class="btn-primary">
                        Submit Record
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Handle money denomination change
    $('input[name="money_denomination"]').on('change', function() {
        if ($(this).val() === 'ESPEES') {
            $('#currency-field').hide();
            $('#currency_code').prop('required', false);
        } else {
            $('#currency-field').show();
            $('#currency_code').prop('required', true);
        }
    });

    // Form validation
    $('#external-form').on('submit', function(e) {
        let isValid = true;
        const requiredFields = [
            'organization_name', 'contact_person', 'contact_email', 'contact_phone',
            'group', 'country',
            'money_denomination', 'money_amount'
        ];

        requiredFields.forEach(function(field) {
            const input = $('[name="' + field + '"]');
            if (!input.val()) {
                isValid = false;
                input.addClass('border-red-500');
                showToast('error', 'Please fill in all required fields');
            } else {
                input.removeClass('border-red-500');
            }
        });

        // Validate email format if provided
        const email = $('#contact_email').val();
        if (email && !isValidEmail(email)) {
            isValid = false;
            $('#contact_email').addClass('border-red-500');
            showToast('error', 'Please enter a valid email address');
        }

        if (!isValid) {
            e.preventDefault();
        }
    });

    // Real-time validation feedback
    $('input[required], select[required]').on('blur', function() {
        if ($(this).val()) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        } else {
            $(this).removeClass('border-green-500').addClass('border-red-500');
        }
    });

    // Email validation
    $('#contact_email').on('blur', function() {
        const email = $(this).val();
        if (email && !isValidEmail(email)) {
            $(this).removeClass('border-green-500').addClass('border-red-500');
        } else if (email) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        }
    });
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Draft functionality removed - records are auto-approved
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
