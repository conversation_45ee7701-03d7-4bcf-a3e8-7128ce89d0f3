<?php
/**
 * User Profile Management Page
 * Allows users (especially RZM) to manage their own account information
 */

require_once __DIR__ . '/../../controllers/admin.php';
require_once __DIR__ . '/../components.php';

// Get current user
$current_user = get_current_app_user();
if (!$current_user) {
    header('Location: ?r=login');
    exit;
}

$user_id = $current_user['id'];
$user = $current_user; // Use current session user data

$page_title = 'My Profile - IPPC Awards Portal';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_csrf();

    $action = $_POST['action'] ?? 'update_profile';

    if ($action === 'update_profile') {
        // Handle profile update
        $update_data = [
            'name' => trim($_POST['name'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
        ];

        // Validate input
        $errors = [];

        if (empty($update_data['name'])) {
            $errors['name'] = 'Name is required';
        } elseif (strlen($update_data['name']) < 2) {
            $errors['name'] = 'Name must be at least 2 characters long';
        }

        if (empty($update_data['email'])) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($update_data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        }

        if (empty($errors)) {
            // Check if email is already taken by another user
            $existing_users = db_read('users');
            foreach ($existing_users as $existing) {
                if ($existing['id'] !== $user_id && $existing['email'] === $update_data['email']) {
                    $errors['email'] = 'This email address is already in use';
                    break;
                }
            }
        }

        if (empty($errors)) {
            try {
                // Update user profile
                $result = db_update('users', $user_id, $update_data);

                if ($result) {
                    // Update session data
                    $_SESSION['user'] = array_merge($_SESSION['user'], $update_data);

                    audit_log($user_id, 'profile_updated', [
                        'fields_updated' => array_keys($update_data)
                    ]);

                    $success_message = 'Profile updated successfully';
                    $user = $_SESSION['user']; // Refresh user data
                } else {
                    $error_message = 'Failed to update profile';
                }
            } catch (Exception $e) {
                $error_message = 'Failed to update profile: ' . $e->getMessage();
            }
        } else {
            $error_message = 'Please correct the errors below';
        }

    } elseif ($action === 'change_password') {
        // Handle password change
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        $errors = [];

        // Verify current password
        if (empty($current_password)) {
            $errors['current_password'] = 'Current password is required';
        } elseif (!password_verify($current_password, $user['password_hash'])) {
            $errors['current_password'] = 'Current password is incorrect';
        }

        // Validate new password
        if (empty($new_password)) {
            $errors['new_password'] = 'New password is required';
        } elseif (strlen($new_password) < 8) {
            $errors['new_password'] = 'New password must be at least 8 characters long';
        }

        if ($new_password !== $confirm_password) {
            $errors['confirm_password'] = 'Passwords do not match';
        }

        if (empty($errors)) {
            try {
                // Update password
                $result = db_update('users', $user_id, [
                    'password_hash' => password_hash($new_password, PASSWORD_DEFAULT)
                ]);

                if ($result) {
                    audit_log($user_id, 'password_changed', []);
                    $success_message = 'Password changed successfully';
                } else {
                    $error_message = 'Failed to change password';
                }
            } catch (Exception $e) {
                $error_message = 'Failed to change password: ' . $e->getMessage();
            }
        } else {
            $error_message = 'Please correct the errors below';
        }
    }
}

// Get user statistics
$user_role = $user['role'] ?? '';
$user_region = $user['region_id'] ?? null;
$user_zone = $user['zone_id'] ?? null;
$approval_status = $user['approval_status'] ?? 'approved';

ob_start();
?>

<div class="max-w-4xl mx-auto space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">My Profile</h1>
            <p class="text-gray-600 mt-1">Manage your account information and settings</p>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($success_message)): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($success_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($error_message) ?>
        <?php if (!empty($errors)): ?>
        <ul class="mt-2 list-disc list-inside">
            <?php foreach ($errors as $field => $error): ?>
            <li><strong><?= htmlspecialchars($field) ?>:</strong> <?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                <form method="POST" class="space-y-4">
                    <?= csrf_token_input() ?>
                    <input type="hidden" name="action" value="update_profile">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?= render_text_input('name', 'Full Name', $user['name'] ?? '', true, 'Enter your full name') ?>

                        <?= render_text_input('email', 'Email Address', $user['email'] ?? '', true, 'Enter your email address', 'email') ?>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary">
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>

            <!-- Password Change -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Change Password</h3>
                <form method="POST" class="space-y-4">
                    <?= csrf_token_input() ?>
                    <input type="hidden" name="action" value="change_password">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="current_password" class="form-label">Current Password <span class="text-red-500">*</span></label>
                            <input type="password" id="current_password" name="current_password"
                                   class="form-input" placeholder="Enter current password" required>
                        </div>

                        <div></div>

                        <div>
                            <label for="new_password" class="form-label">New Password <span class="text-red-500">*</span></label>
                            <input type="password" id="new_password" name="new_password"
                                   class="form-input" placeholder="Enter new password" required minlength="8">
                            <p class="mt-1 text-sm text-gray-500">Must be at least 8 characters long</p>
                        </div>

                        <div>
                            <label for="confirm_password" class="form-label">Confirm New Password <span class="text-red-500">*</span></label>
                            <input type="password" id="confirm_password" name="confirm_password"
                                   class="form-input" placeholder="Confirm new password" required minlength="8">
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary">
                            Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Account Information Sidebar -->
        <div class="space-y-6">
            <!-- Account Status -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Status</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Status:</span>
                        <span class="px-2 py-1 text-xs font-medium rounded-full
                            <?= ($user['status'] ?? 'active') === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                            <?= ucfirst($user['status'] ?? 'active') ?>
                        </span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Approval:</span>
                        <span class="px-2 py-1 text-xs font-medium rounded-full
                            <?= match($approval_status) {
                                'approved' => 'bg-green-100 text-green-800',
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'denied' => 'bg-red-100 text-red-800',
                                default => 'bg-gray-100 text-gray-800'
                            } ?>">
                            <?= ucfirst($approval_status) ?>
                        </span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Role:</span>
                        <span class="px-2 py-1 text-xs font-medium rounded-full
                            <?= $user_role === 'ADMIN' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800' ?>">
                            <?= htmlspecialchars($user_role) ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Regional Information (for RZM users) -->
            <?php if ($user_role === 'RZM'): ?>
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Regional Assignment</h3>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm text-gray-600">Region:</span>
                        <div class="font-medium text-gray-900">
                            <?php
                            $regions = get_lookup_options('regions');
                            echo htmlspecialchars($regions[$user_region] ?? 'Region ' . $user_region);
                            ?>
                        </div>
                    </div>

                    <div>
                        <span class="text-sm text-gray-600">Zone:</span>
                        <div class="font-medium text-gray-900">
                            <?= htmlspecialchars($user_zone) ?>
                        </div>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-sm text-blue-800">
                        <strong>Note:</strong> Regional assignments can only be changed by an administrator.
                        Contact your system administrator if you need to change your region or zone assignment.
                    </p>
                </div>
            </div>
            <?php endif; ?>

            <!-- Account Information -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">User ID:</span>
                        <span class="font-mono text-gray-900"><?= htmlspecialchars($user['id']) ?></span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-600">Created:</span>
                        <span class="text-gray-900">
                            <?= date('M j, Y', strtotime($user['created_at'] ?? '')) ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Password confirmation validation
    $('#confirm_password').on('blur', function() {
        const newPassword = $('#new_password').val();
        const confirmPassword = $(this).val();

        if (newPassword && confirmPassword && newPassword !== confirmPassword) {
            $(this).addClass('border-red-500');
            showToast('error', 'Passwords do not match');
        } else {
            $(this).removeClass('border-red-500');
            if (newPassword && confirmPassword) {
                $(this).addClass('border-green-500');
            }
        }
    });

    // Password strength validation
    $('#new_password').on('blur', function() {
        const password = $(this).val();
        if (password && password.length < 8) {
            $(this).addClass('border-red-500');
            showToast('error', 'Password must be at least 8 characters long');
        } else if (password) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        const action = $(this).find('input[name="action"]').val();

        if (action === 'change_password') {
            const currentPassword = $('#current_password').val();
            const newPassword = $('#new_password').val();
            const confirmPassword = $('#confirm_password').val();

            let isValid = true;

            if (!currentPassword) {
                isValid = false;
                $('#current_password').addClass('border-red-500');
            }

            if (!newPassword || newPassword.length < 8) {
                isValid = false;
                $('#new_password').addClass('border-red-500');
            }

            if (newPassword !== confirmPassword) {
                isValid = false;
                $('#confirm_password').addClass('border-red-500');
            }

            if (!isValid) {
                e.preventDefault();
                showToast('error', 'Please correct the password errors');
            }
        } else if (action === 'update_profile') {
            const name = $('#name').val();
            const email = $('#email').val();

            let isValid = true;

            if (!name || name.length < 2) {
                isValid = false;
                $('#name').addClass('border-red-500');
            }

            if (!email || !isValidEmail(email)) {
                isValid = false;
                $('#email').addClass('border-red-500');
            }

            if (!isValid) {
                e.preventDefault();
                showToast('error', 'Please correct the profile errors');
            }
        }
    });
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
