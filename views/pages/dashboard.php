<?php
/**
 * Dashboard Page
 * Shows KPIs, filters, data table, and top-N widgets
 */

require_once __DIR__ . '/../../controllers/search.php';
require_once __DIR__ . '/../components.php';

$page_title = 'Dashboard - IPPC Awards Portal';

// Get initial data for KPIs
$search_result = search();
$summary = $search_result['summary'];

ob_start();
?>

<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
        <div class="flex space-x-4">
            <button onclick="refreshData()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
            <a href="?r=submit_category_b" class="btn-primary">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                New Submission
            </a>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <?= render_kpi_card(
            'Total Records', 
            number_format($summary['total_records']), 
            '<div class="text-sm text-gray-500 mt-1">All submissions</div>'
        ) ?>
        
        <?= render_kpi_card(
            'Total ESPEES', 
            number_format($summary['total_espees'], 2), 
            '<div class="text-sm text-gray-500 mt-1">Normalized global value</div>'
        ) ?>
        
        <?= render_kpi_card(
            'Average ESPEES', 
            number_format($summary['average_espees'], 2), 
            '<div class="text-sm text-gray-500 mt-1">Per record</div>'
        ) ?>
        

    </div>


        
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Entity Type Breakdown</h3>
            <div class="space-y-3">
                <?php foreach ($summary['entity_counts'] as $type => $count): ?>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600"><?= htmlspecialchars($type) ?></span>
                    <span class="font-medium"><?= number_format($count) ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <?= render_filter_panel([
        [
            'type' => 'text',
            'name' => 'q',
            'label' => 'Search',
            'placeholder' => 'Search records...'
        ],
        [
            'type' => 'select',
            'name' => 'entity_types[]',
            'label' => 'Entity Type',
            'options' => get_lookup_options('entity_types'),
            'placeholder' => 'All Types'
        ],
        [
            'type' => 'select',
            'name' => 'region_ids[]',
            'label' => 'Region',
            'options' => get_lookup_options('regions'),
            'placeholder' => 'All Regions'
        ],
        [
            'type' => 'select',
            'name' => 'zone_ids[]',
            'label' => 'Zone',
            'options' => get_lookup_options('zones'),
            'placeholder' => 'All Zones'
        ],
        [
            'type' => 'select',
            'name' => 'countries[]',
            'label' => 'Country',
            'options' => get_lookup_options('countries'),
            'placeholder' => 'All Countries'
        ],
        [
            'type' => 'select',
            'name' => 'groups[]',
            'label' => 'Group',
            'options' => get_lookup_options('groups'),
            'placeholder' => 'All Groups'
        ],

    ]) ?>

    <!-- Data Table -->
    <div class="card p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Records</h3>
            <div class="flex space-x-2">
                <button onclick="exportData()" class="btn-secondary text-sm">
                    <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
            </div>
        </div>
        
        <table id="records-table" class="display w-full">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Entity Type</th>
                    <th>Name</th>
                    <th>Zone</th>
                    <th>Group</th>
                    <th>Church</th>
                    <th>Country</th>
                    <th>ESPEES</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- Data will be loaded via AJAX -->
            </tbody>
        </table>
    </div>

    <!-- Top-N Widgets -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="card p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Top Zones by ESPEES</h3>
                <select id="top-zones-count" class="text-sm border-gray-300 rounded-lg">
                    <option value="10">Top 10</option>
                    <option value="25">Top 25</option>
                    <option value="50">Top 50</option>
                </select>
            </div>
            <div id="top-zones-content" class="space-y-2">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
        
        <div class="card p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Top Countries by ESPEES</h3>
                <select id="top-countries-count" class="text-sm border-gray-300 rounded-lg">
                    <option value="10">Top 10</option>
                    <option value="25">Top 25</option>
                    <option value="50">Top 50</option>
                </select>
            </div>
            <div id="top-countries-content" class="space-y-2">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    const table = $('#records-table').DataTable({
        processing: true,
        serverSide: false,
        ajax: function(data, callback, settings) {
            console.log('DataTable making AJAX request');

            // Use simple jQuery AJAX like the top widgets
            $.ajax({
                url: '?r=search',
                type: 'GET',
                dataType: 'json',
                xhrFields: {
                    withCredentials: true
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                data: data,
                success: function(json) {
                    console.log('DataTable AJAX success:', json);
                    // Update summary stats
                    updateSummaryStats(json.summary);
                    callback({
                        data: json.rows || []
                    });
                },
                error: function(xhr, error, thrown) {
                    console.error('DataTable AJAX error:', error, thrown);
                    console.log('Response:', xhr.responseText);
                    console.log('Status:', xhr.status);
                    if (xhr.status === 401) {
                        window.location.href = '?r=login';
                    }
                    // Return empty data on error to prevent infinite loading
                    callback({
                        data: []
                    });
                }
            });
        },
        columns: [
            { data: 'id', render: function(data) { return (data ? String(data).substring(0, 8) : '-') + (data ? '...' : ''); } },
            { data: 'entity_type', defaultContent: '-' },
            {
                data: null,
                render: function(data, type, row) {
                    if (!row) return '-';
                    // Display name based on entity type
                    if (row.entity_type === 'External') {
                        return row.organization_name || '-';
                    } else if (row.name) {
                        return row.name;
                    } else if (row.first_name && row.surname) {
                        return row.first_name + ' ' + row.surname;
                    }
                    return '-';
                },
                defaultContent: '-'
            },
            { data: 'zone_id', defaultContent: '-' },
            { data: 'group', defaultContent: '-' },
            { data: 'church', defaultContent: '-' },
            { data: 'country.code', defaultContent: '-' },
            {
                data: 'measures_norm.espees',
                render: function(data) {
                    return Number(data || 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                },
                defaultContent: '0.00'
            },

            {
                data: null,
                render: function(data, type, row) {
                    if (!row) return '-';
                    let actions = [];

                    // All records are approved, show edit action
                    actions.push('<button onclick="editRecord(\'' + row.id + '\')" class="text-blue-600 hover:text-blue-800 text-sm">Edit</button>');

                    return actions.join(' | ') || '-';
                },
                orderable: false
            }
        ],
        pageLength: 25,
        order: [[6, 'desc']], // Sort by ESPEES descending
        responsive: true
    });

    // Cascading filter handlers
    $('#filter-form select[name="region_ids[]"]').on('change', function() {
        const selectedRegion = $(this).val();
        const zoneSelect = $('#filter-form select[name="zone_ids[]"]');

        if (selectedRegion) {
            // Filter zones by selected region
            // Convert region number to "Region X" format for API call
            const regionKey = 'Region ' + selectedRegion;
            $.get('?ajax=zones&region=' + encodeURIComponent(regionKey))
                .done(function(data) {
                    zoneSelect.empty().append('<option value="">All Zones</option>');
                    $.each(data.zones, function(value, text) {
                        zoneSelect.append('<option value="' + value + '">' + text + '</option>');
                    });
                })
                .fail(function() {
                    console.error('Failed to load zones for region');
                });
        } else {
            // Reset to show all zones
            $.get('?ajax=zones')
                .done(function(data) {
                    zoneSelect.empty().append('<option value="">All Zones</option>');
                    $.each(data.zones, function(value, text) {
                        zoneSelect.append('<option value="' + value + '">' + text + '</option>');
                    });
                });
        }

        // Clear dependent filters
        $('#filter-form select[name="zone_ids[]"]').val('');
    });

    $('#filter-form select[name="zone_ids[]"]').on('change', function() {
        const selectedZone = $(this).val();
        const groupSelect = $('#filter-form select[name="groups[]"]');

        if (selectedZone) {
            // Filter groups by selected zone
            $.get('?ajax=groups&zone=' + encodeURIComponent(selectedZone))
                .done(function(data) {
                    groupSelect.empty().append('<option value="">All Groups</option>');
                    $.each(data.groups, function(value, text) {
                        groupSelect.append('<option value="' + value + '">' + text + '</option>');
                    });
                })
                .fail(function() {
                    console.error('Failed to load groups for zone');
                });
        } else {
            // Reset to show all groups
            $.get('?ajax=groups')
                .done(function(data) {
                    groupSelect.empty().append('<option value="">All Groups</option>');
                    $.each(data.groups, function(value, text) {
                        groupSelect.append('<option value="' + value + '">' + text + '</option>');
                    });
                });
        }

        // Clear dependent filters
        $('#filter-form select[name="groups[]"]').val('');
    });

    // Filter form submission
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        table.ajax.reload();
        loadTopWidgets();
    });

    // Clear filters
    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();

        // Reset cascading filters to original state
        const zoneSelect = $('#filter-form select[name="zone_ids[]"]');
        const groupSelect = $('#filter-form select[name="groups[]"]');

        zoneSelect.empty().append('<option value="">All Zones</option>');
        groupSelect.empty().append('<option value="">All Groups</option>');

        // Reload original zone and group options
        $.get('?ajax=zones').done(function(data) {
            $.each(data.zones, function(value, text) {
                zoneSelect.append('<option value="' + value + '">' + text + '</option>');
            });
        });

        $.get('?ajax=groups').done(function(data) {
            $.each(data.groups, function(value, text) {
                groupSelect.append('<option value="' + value + '">' + text + '</option>');
            });
        });

        table.ajax.reload();
        loadTopWidgets();
    });

    // Top widgets count change
    $('#top-zones-count, #top-countries-count').on('change', function() {
        loadTopWidgets();
    });

    // Load initial top widgets
    loadTopWidgets();
});

function updateSummaryStats(summary) {
    // This would update the KPI cards with new data
    // Implementation depends on specific needs
}

function loadTopWidgets() {
    // Load top zones
    const zonesCount = $('#top-zones-count').val();
    $.get('?r=top', {
        level: 'zone',
        metric: 'espees',
        n: zonesCount
    }).done(function(data) {
        let html = '';
        data.forEach(function(item, index) {
            html += `
            <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <div class="flex items-center">
                    <span class="w-6 h-6 bg-primary text-white text-xs rounded-full flex items-center justify-center mr-3">${item.rank}</span>
                    <span class="text-sm font-medium">${item.key}</span>
                </div>
                <span class="text-sm text-gray-600">${item.formatted_value}</span>
            </div>`;
        });
        $('#top-zones-content').html(html || '<p class="text-gray-500 text-sm">No data available</p>');
    });

    // Load top countries
    const countriesCount = $('#top-countries-count').val();
    $.get('?r=top', {
        level: 'country',
        metric: 'espees',
        n: countriesCount
    }).done(function(data) {
        let html = '';
        data.forEach(function(item, index) {
            html += `
            <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <div class="flex items-center">
                    <span class="w-6 h-6 bg-primary text-white text-xs rounded-full flex items-center justify-center mr-3">${item.rank}</span>
                    <span class="text-sm font-medium">${item.key}</span>
                </div>
                <span class="text-sm text-gray-600">${item.formatted_value}</span>
            </div>`;
        });
        $('#top-countries-content').html(html || '<p class="text-gray-500 text-sm">No data available</p>');
    });
}

function refreshData() {
    $('#records-table').DataTable().ajax.reload();
    loadTopWidgets();
    showToast('success', 'Data refreshed successfully');
}

function editRecord(recordId) {
    // Implement edit functionality
    showToast('info', 'Edit functionality would be implemented here');
}





function exportData() {
    showToast('info', 'Export functionality would be implemented here');
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
