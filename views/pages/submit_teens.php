<?php
/**
 * Partners - Teens Submission Form
 * Teen partner contributions submission form
 */

require_once __DIR__ . '/../../app/db.php';
require_once __DIR__ . '/../../controllers/records.php';
require_once __DIR__ . '/../components.php';

$page_title = 'Submit Partners - Teens';

// Load form configuration
$form_config = db_read('forms/partners_teens');
$program_registry = db_read('program_registry');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = submit_record($_POST);

    if ($result['success']) {
        header('Location: ?r=dashboard&success=' . urlencode($result['message']));
        exit;
    } else {
        $errors = $result['errors'] ?? [];
        $error_message = $result['error'] ?? 'Submission failed';
    }
}

ob_start();
?>

<div class="max-w-4xl mx-auto space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Partners - Teens</h1>
            <p class="text-gray-600 mt-1">Submit data for teen partner contributions and activities</p>
        </div>
        <a href="?r=dashboard" class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($error_message) ?>
        <?php if (!empty($errors)): ?>
        <ul class="mt-2 list-disc list-inside">
            <?php foreach ($errors as $field => $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Main Form -->
    <form method="POST" class="space-y-6" id="teens-form">
        <?= csrf_token_input() ?>
        <input type="hidden" name="entity_type" value="Teens">

        <!-- Personal Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php
                $personal_fields = ['title', 'name', 'kingschat_phone', 'email', 'birthday'];

                foreach ($form_config['fields'] as $field):
                    if (in_array($field['name'], $personal_fields)):
                        if ($field['type'] === 'select' && !isset($field['options'])):
                            // Handle hardcoded select options like title
                            $options = [];
                            if ($field['name'] === 'title') {
                                $options = [
                                    "Mr" => "Mr",
                                    "Miss" => "Miss",
                                    "Ms" => "Ms",
                                    "Other" => "Other"
                                ];
                            }
                            echo render_select_input($field['name'], $field['label'], $options, $_POST[$field['name']] ?? '', $field['required'] ?? false);
                        elseif ($field['type'] === 'text' || $field['type'] === 'tel' || $field['type'] === 'email' || $field['type'] === 'date'):
                            $input_type = $field['type'];
                            if ($field['type'] === 'date') {
                                ?>
                                <div>
                                    <label for="<?= $field['name'] ?>" class="form-label">
                                        <?= htmlspecialchars($field['label']) ?>
                                        <?php if ($field['required'] ?? false): ?><span class="text-red-500">*</span><?php endif; ?>
                                    </label>
                                    <input type="date" id="<?= $field['name'] ?>" name="<?= $field['name'] ?>"
                                           value="<?= htmlspecialchars($_POST[$field['name']] ?? '') ?>" class="form-input"
                                           <?php if ($field['required'] ?? false): ?>required<?php endif; ?>>
                                </div>
                                <?php
                            } else {
                                echo render_text_input($field['name'], $field['label'], $_POST[$field['name']] ?? '', $field['required'] ?? false, $field['placeholder'] ?? '', $input_type);
                            }
                        endif;
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Location & Affiliation -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Location & Affiliation</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php
                $current_user = get_current_app_user();
                $is_admin = is_admin();
                $is_rzm = isset($current_user['role']) && $current_user['role'] === 'RZM';

                $location_fields = ['church', 'group', 'zone', 'region_id', 'country'];

                foreach ($form_config['fields'] as $field):
                    if (in_array($field['name'], $location_fields)):
                        if ($field['type'] === 'text'):
                            echo render_text_input($field['name'], $field['label'], $_POST[$field['name']] ?? '', $field['required'] ?? false, $field['placeholder'] ?? '');
                        elseif ($field['type'] === 'select'):
                            // Handle region/zone fields with auto-determination for RZM/Admin
                            if ($field['options'] === 'regions' || $field['options'] === 'zones') {
                                // Hide region and zone fields completely for RZM users
                                if (($field['options'] === 'regions' || $field['options'] === 'zones') && $is_rzm) {
                                    continue; // Don't show these fields at all for RZM
                                }

                                // Auto-determine region/zone for admin users only
                                $selected_value = $_POST[$field['name']] ?? '';
                                if (empty($selected_value) && $is_admin) {
                                    if ($field['options'] === 'regions' && isset($current_user['region_id'])) {
                                        $selected_value = $current_user['region_id'];
                                    } elseif ($field['options'] === 'zones' && isset($current_user['zone_id'])) {
                                        $selected_value = $current_user['zone_id'];
                                    }
                                }

                                // For admin users, make region/zone readonly and auto-selected
                                $readonly = $is_admin ? 'readonly' : '';
                                $required = $is_admin ? false : ($field['required'] ?? false);
                                $user_type = $is_admin ? 'Admin' : ($is_rzm ? 'RZM' : '');
                                $label_suffix = $is_admin ? " <span class=\"text-xs text-secondary-500\">(Auto-determined for {$user_type})</span>" : '';
                                ?>
                                <div class="mb-4">
                                    <label for="<?= $field['name'] ?>" class="form-label">
                                        <?= htmlspecialchars($field['label']) ?><?= $label_suffix ?>
                                        <?php if ($required): ?><span class="text-red-500">*</span><?php endif; ?>
                                    </label>
                                    <select id="<?= $field['name'] ?>" name="<?= $field['name'] ?>" class="form-select" <?= $readonly ?> <?= $required ? 'required' : '' ?>>
                                        <option value="">Select <?= htmlspecialchars($field['label']) ?></option>
                                        <?php foreach (get_lookup_options($field['options']) as $value => $text): ?>
                                            <option value="<?= htmlspecialchars($value) ?>" <?= $value == $selected_value ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($text) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php
                            } elseif ($field['options'] === 'groups') {
                                // Handle group field - make it optional and filter by zone
                                $selected_value = $_POST[$field['name']] ?? '';
                                ?>
                                <div class="mb-4">
                                    <label for="<?= $field['name'] ?>" class="form-label">
                                        <?= htmlspecialchars($field['label']) ?>
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <select id="<?= $field['name'] ?>" name="<?= $field['name'] ?>" class="form-select">
                                        <option value="">Select Group *</option>
                                        <?php foreach (get_lookup_options($field['options']) as $value => $text): ?>
                                            <option value="<?= htmlspecialchars($value) ?>" <?= $value == $selected_value ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($text) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php
                            } else {
                                echo render_select_input($field['name'], $field['label'], get_lookup_options($field['options']), $_POST[$field['name']] ?? '', $field['required'] ?? false);
                            }
                        endif;
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Financial Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <?= render_radio_group('money_denomination', 'Money Denomination', [
                    ['value' => 'CURRENCY', 'label' => 'Local Currency'],
                    ['value' => 'ESPEES', 'label' => 'ESPEES']
                ], $_POST['money_denomination'] ?? 'CURRENCY', true) ?>

                <?= render_number_input('money_amount', 'Total Amount', $_POST['money_amount'] ?? 0, true, 0, '0.01', '0.00') ?>

                <div id="currency-field" class="<?= ($_POST['money_denomination'] ?? 'CURRENCY') === 'ESPEES' ? 'hidden' : '' ?>">
                    <?= render_select_input('currency_code', 'Currency Code', get_lookup_options('currencies'), $_POST['currency_code'] ?? '', false) ?>
                </div>
            </div>
        </div>

        <!-- Program Contributions -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Program Contributions</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php
                $category_registry = db_read('category_registry');
                $partners_teens = null;
                foreach ($category_registry['categories'] as $cat) {
                    if ($cat['id'] === 'partners_teens') {
                        $partners_teens = $cat;
                        break;
                    }
                }

                $allowed_programs = $partners_teens['programs_allowed'] ?? [];
                foreach ($program_registry['programs'] as $program):
                    if (!in_array($program['key'], $allowed_programs)) continue;

                    $step = $program['type'] === 'money' ? '0.01' : '1';
                    $placeholder = $program['type'] === 'money' ? '0.00' : '0';
                ?>
                    <?= render_number_input(
                        $program['key'],
                        $program['label'] . ($program['type'] === 'count' ? ' (Count)' : ''),
                        $_POST[$program['key']] ?? 0,
                        false,
                        0,
                        $step,
                        $placeholder
                    ) ?>
                <?php endforeach; ?>
            </div>

            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">Program Types</h4>
                <div class="text-sm text-blue-800 space-y-1">
                    <div><strong>Money Programs:</strong> Enter monetary amounts that will be normalized to global ESPEES</div>
                    <div><strong>Total Amount Received:</strong> Total monetary contributions received from this teen partner</div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
            <div class="space-y-4">
                <div>
                    <label for="youth_group" class="form-label">Youth Group/Ministry (Optional)</label>
                    <input type="text" id="youth_group" name="youth_group"
                           value="<?= htmlspecialchars($_POST['youth_group'] ?? '') ?>"
                           class="form-input" placeholder="Enter youth group or ministry name">
                </div>
                <div>
                    <label for="interests" class="form-label">Interests/Hobbies (Optional)</label>
                    <textarea id="interests" name="interests" rows="2" class="form-input"
                              placeholder="Enter teen's interests or hobbies..."><?= htmlspecialchars($_POST['interests'] ?? '') ?></textarea>
                </div>
                <div>
                    <label for="notes" class="form-label">Notes (Optional)</label>
                    <textarea id="notes" name="notes" rows="3" class="form-input"
                              placeholder="Enter any additional notes or comments about this teen partner..."><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                </div>
            </div>
        </div>

        <!-- Submit Actions -->
        <div class="card p-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    <p>Your submission will be saved and approved immediately.</p>
                </div>
                <div class="flex space-x-4">
                    <button type="submit" class="btn-primary">
                        Submit Record
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Handle money denomination change
    $('input[name="money_denomination"]').on('change', function() {
        if ($(this).val() === 'ESPEES') {
            $('#currency-field').hide();
            $('#currency_code').prop('required', false);
        } else {
            $('#currency-field').show();
            $('#currency_code').prop('required', true);
        }
    });

    // Form validation
    $('#teens-form').on('submit', function(e) {
        let isValid = true;
        const requiredFields = [
            'name', 'kingschat_phone', 'email', 'group', 'country', 'money_denomination', 'money_amount'
            <?php
            $current_user = get_current_app_user();
            $is_admin = is_admin();
            $is_rzm = isset($current_user['role']) && $current_user['role'] === 'RZM';

            // Exclude region/zone fields from validation for RZM users since they're auto-determined
            if (!$is_rzm) {
                echo ", 'region_id', 'zone_id'";
            }
            ?>
        ];

        requiredFields.forEach(function(field) {
            const input = $('[name="' + field + '"]');
            if (!input.val()) {
                isValid = false;
                input.addClass('border-red-500');
                showToast('error', 'Please fill in all required fields');
            } else {
                input.removeClass('border-red-500');
            }
        });

        // Validate email format if provided
        const email = $('#email').val();
        if (email && !isValidEmail(email)) {
            isValid = false;
            $('#email').addClass('border-red-500');
            showToast('error', 'Please enter a valid email address');
        }

        if (!isValid) {
            e.preventDefault();
        }
    });

    // Handle zone change to dynamically load groups
    $('select[name="zone_id"]').on('change', function() {
        const selectedZone = $(this).val();
        const groupSelect = $('select[name="group"]');

        if (selectedZone) {
            // Get the zone display text for the AJAX call
            const selectedZoneText = $(this).find('option:selected').text();

            // Make AJAX call to get filtered groups
            $.ajax({
                url: '?ajax=groups&zone=' + encodeURIComponent(selectedZoneText),
                method: 'GET',
                success: function(data) {
                    groupSelect.empty().append('<option value="">Select Group *</option>');
                    $.each(data.groups, function(value, text) {
                        groupSelect.append('<option value="' + value + '">' + text + '</option>');
                    });
                },
                error: function() {
                    // Fallback: reload page if AJAX fails
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('zone', selectedZoneText);
                    window.location.href = currentUrl.toString();
                }
            });
        } else {
            // No zone selected, clear groups
            groupSelect.empty().append('<option value="">Select Zone first</option>');
        }
    });

    // Real-time validation feedback
    $('input[required], select[required]').on('blur', function() {
        if ($(this).val()) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        } else {
            $(this).removeClass('border-green-500').addClass('border-red-500');
        }
    });

    // Email validation
    $('#email').on('blur', function() {
        const email = $(this).val();
        if (email && !isValidEmail(email)) {
            $(this).removeClass('border-green-500').addClass('border-red-500');
        } else if (email) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        }
    });
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Draft functionality removed - records are auto-approved
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
