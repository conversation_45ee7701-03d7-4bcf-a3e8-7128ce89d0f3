<?php
/**
 * User Registration Page
 * Allows new users to create accounts that require admin approval
 */

$page_title = 'Register - IPPC Awards Portal';
$error_message = '';
$success_message = '';

require_once __DIR__ . '/../../app/db.php';
require_once __DIR__ . '/../../app/auth.php';

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_csrf();

    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $password_confirm = $_POST['password_confirm'] ?? '';
    $role = 'RZM'; // Default to RZM role for new registrations
    $region_id = $_POST['region_id'] ?? '';
    $zone_id = trim($_POST['zone_id'] ?? '');

    $errors = [];

    // Validation
    if (empty($name)) {
        $errors['name'] = 'Name is required';
    } elseif (strlen($name) < 2) {
        $errors['name'] = 'Name must be at least 2 characters long';
    }

    if (empty($email)) {
        $errors['email'] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Please enter a valid email address';
    }

    if (empty($password)) {
        $errors['password'] = 'Password is required';
    } elseif (strlen($password) < 8) {
        $errors['password'] = 'Password must be at least 8 characters long';
    }

    if ($password !== $password_confirm) {
        $errors['password_confirm'] = 'Passwords do not match';
    }

    // Always require region and zone for RZM role (which is now default)
    if (empty($region_id)) {
        $errors['region_id'] = 'Region is required';
    }
    if (empty($zone_id)) {
        $errors['zone_id'] = 'Zone is required';
    }

    if (empty($errors)) {
        try {
            // Check if email already exists
            $existing_users = db_read('users');
            foreach ($existing_users as $existing) {
                if ($existing['email'] === $email) {
                    $errors['email'] = 'A user with this email address already exists';
                    break;
                }
            }

            if (empty($errors)) {
                // Generate user ID
                $user_id = generate_ulid();

                // Prepare user data with pending approval
                $user = [
                    'id' => $user_id,
                    'name' => $name,
                    'email' => $email,
                    'password_hash' => password_hash($password, PASSWORD_DEFAULT),
                    'role' => $role,
                    'status' => 'active',
                    'approval_status' => 'pending',
                    'created_at' => date('c'),
                    'created_by' => null,
                    'last_login' => null
                ];

                // Add RZM-specific fields
                if ($role === 'RZM') {
                    $user['region_id'] = intval($region_id);
                    $user['zone_id'] = $zone_id;
                }

                // Save user
                db_append('users', $user);

                // Log the registration
                audit_log($user_id, 'user_registered', [
                    'email' => $email,
                    'role' => $role,
                    'approval_status' => 'pending'
                ]);

                $success_message = 'Your account has been created successfully! Please wait for admin approval before you can log in.';

                // Clear form data
                $name = $email = $password = $password_confirm = $region_id = $zone_id = '';
            }
        } catch (Exception $e) {
            $error_message = 'Failed to create account. Please try again.';
        }
    } else {
        $error_message = 'Please correct the errors below.';
    }
}

// Load lookups for regions and zones
$lookups = db_read('lookups');

ob_start();
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">
                Create Account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Sign up for an IPPC Awards Portal account
            </p>
        </div>

        <?php if ($error_message && empty($success_message)): ?>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl">
            <?= htmlspecialchars($success_message) ?>
        </div>

        <div class="text-center">
            <a href="?r=login" class="text-blue-600 hover:text-blue-500 font-medium">
                ← Back to Login
            </a>
        </div>
        <?php else: ?>

        <form class="mt-8 space-y-6 card p-6" method="POST">
            <?= csrf_token_input() ?>

            <div class="space-y-4">
                <div>
                    <label for="name" class="form-label">Full Name <span class="text-red-500">*</span></label>
                    <input id="name" name="name" type="text" required
                           class="form-input" placeholder="Enter your full name"
                           value="<?= htmlspecialchars($name ?? '') ?>">
                    <?php if (isset($errors['name'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= htmlspecialchars($errors['name']) ?></p>
                    <?php endif; ?>
                </div>

                <div>
                    <label for="email" class="form-label">Email Address <span class="text-red-500">*</span></label>
                    <input id="email" name="email" type="email" required
                           class="form-input" placeholder="Enter your email"
                           value="<?= htmlspecialchars($email ?? '') ?>">
                    <?php if (isset($errors['email'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= htmlspecialchars($errors['email']) ?></p>
                    <?php endif; ?>
                </div>

                <div>
                    <label for="password" class="form-label">Password <span class="text-red-500">*</span></label>
                    <input id="password" name="password" type="password" required
                           class="form-input" placeholder="Create a password (min 8 characters)"
                           minlength="8">
                    <?php if (isset($errors['password'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= htmlspecialchars($errors['password']) ?></p>
                    <?php endif; ?>
                </div>

                <div>
                    <label for="password_confirm" class="form-label">Confirm Password <span class="text-red-500">*</span></label>
                    <input id="password_confirm" name="password_confirm" type="password" required
                           class="form-input" placeholder="Confirm your password"
                           minlength="8">
                    <?php if (isset($errors['password_confirm'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= htmlspecialchars($errors['password_confirm']) ?></p>
                    <?php endif; ?>
                </div>

                <div>
                    <label for="region_id" class="form-label">Region <span class="text-red-500">*</span></label>
                    <select id="region_id" name="region_id" required class="form-input">
                        <option value="">Select a region</option>
                        <?php foreach (($lookups['regions'] ?? []) as $region): ?>
                            <option value="<?= htmlspecialchars($region['id']) ?>" <?= ($region_id ?? '') == $region['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($region['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (isset($errors['region_id'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= htmlspecialchars($errors['region_id']) ?></p>
                    <?php endif; ?>
                </div>

                <div>
                    <label for="zone_id" class="form-label">Zone <span class="text-red-500">*</span></label>
                    <select id="zone_id" name="zone_id" required class="form-input">
                        <option value="">Select a zone</option>
                        <?php foreach (($lookups['zones'] ?? []) as $zone): ?>
                            <option value="<?= htmlspecialchars($zone['id']) ?>" <?= ($zone_id ?? '') === $zone['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($zone['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (isset($errors['zone_id'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= htmlspecialchars($errors['zone_id']) ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <div class="pt-4">
                <button type="submit" class="btn-primary w-full">
                    Create Account
                </button>
            </div>
        </form>

        <div class="text-center">
            <p class="text-sm text-gray-600">
                Already have an account?
                <a href="?r=login" class="text-blue-600 hover:text-blue-500 font-medium">
                    Sign In
                </a>
            </p>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
