<?php
/**
 * Category B Submission Form
 * Group Churches submission form
 */

require_once __DIR__ . '/../../app/db.php';
require_once __DIR__ . '/../../controllers/records.php';
require_once __DIR__ . '/../components.php';

$page_title = 'Submit Category B - Group Churches';

// Load form configuration
$form_config = db_read('forms/category_b');
$program_registry = db_read('program_registry');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = submit_record($_POST);
    
    if ($result['success']) {
        header('Location: ?r=dashboard&success=' . urlencode($result['message']));
        exit;
    } else {
        $errors = $result['errors'] ?? [];
        $error_message = $result['error'] ?? 'Submission failed';
    }
}

ob_start();
?>

<div class="max-w-4xl mx-auto space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Category B - Group Churches</h1>
            <p class="text-gray-600 mt-1">Submit data for group church activities and programs</p>
        </div>
        <a href="?r=dashboard" class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($error_message) ?>
        <?php if (!empty($errors)): ?>
        <ul class="mt-2 list-disc list-inside">
            <?php foreach ($errors as $field => $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Main Form -->
    <form method="POST" class="space-y-6" id="category-b-form">
        <?= csrf_token_input() ?>
                <input type="hidden" name="entity_type" value="CategoryB">

        <!-- Church Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Church Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php
                $current_user = get_current_app_user();
                $is_admin = is_admin();
                $is_rzm = isset($current_user['role']) && $current_user['role'] === 'RZM';

                foreach ($form_config['fields'] as $field):
                    if ($field['type'] === 'text'):
                        // For admin and RZM users, pre-fill church_name if available
                        $value = $_POST[$field['name']] ?? '';
                        if (($is_admin || $is_rzm) && $field['name'] === 'church_name' && empty($value)) {
                            $value = $current_user['name'] ?? '';
                        }
                        echo render_text_input($field['name'], $field['label'], $value, $field['required'] ?? false, $field['placeholder'] ?? '');
                    elseif ($field['type'] === 'month'):
                        echo render_month_input($field['name'], $field['label'], $_POST[$field['name']] ?? '', $field['required'] ?? false);
                    elseif ($field['type'] === 'select'):
                        // Hide region and zone fields completely for RZM users
                        if (($field['options'] === 'regions' || $field['options'] === 'zones') && $is_rzm) {
                            // For RZM users, don't show region/zone fields at all - they're auto-determined
                            continue;
                        }

                        if ($field['options'] === 'regions' || $field['options'] === 'zones') {
                            // Auto-determine region/zone for admin users only
                            $selected_value = $_POST[$field['name']] ?? '';
                            if (empty($selected_value) && $is_admin) {
                                if ($field['options'] === 'regions' && isset($current_user['region_id'])) {
                                    $selected_value = $current_user['region_id'];
                                } elseif ($field['options'] === 'zones' && isset($current_user['zone_id'])) {
                                    $selected_value = $current_user['zone_id'];
                                }
                            }

                            // For admin/RZM users, make region/zone readonly and auto-selected
                            $readonly = ($is_admin || $is_rzm) ? 'readonly' : '';
                            $required = ($is_admin || $is_rzm) ? false : ($field['required'] ?? false);
                            $user_type = $is_admin ? 'Admin' : ($is_rzm ? 'RZM' : '');
                            $label_suffix = ($is_admin || $is_rzm) ? " <span class=\"text-xs text-secondary-500\">(Auto-determined for {$user_type})</span>" : '';
                            ?>
                            <div class="mb-4">
                                <label for="<?= $field['name'] ?>" class="form-label">
                                    <?= htmlspecialchars($field['label']) ?><?= $label_suffix ?>
                                    <?php if ($required): ?><span class="text-red-500">*</span><?php endif; ?>
                                </label>
                                <select id="<?= $field['name'] ?>" name="<?= $field['name'] ?>" class="form-select" <?= $readonly ?> <?= $required ? 'required' : '' ?>>
                                    <option value="">Select <?= htmlspecialchars($field['label']) ?></option>
                                    <?php foreach (get_lookup_options($field['options']) as $value => $text): ?>
                                        <option value="<?= htmlspecialchars($value) ?>" <?= $value == $selected_value ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($text) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php
                        } else {
                            echo render_select_input($field['name'], $field['label'], get_lookup_options($field['options']), $_POST[$field['name']] ?? '', $field['required'] ?? false);
                        }
                    endif;
                endforeach;
                ?>
            </div>
        </div>

                <!-- Financial Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Financial Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <?php
                // Render remaining fields (radio, number, conditional select)
                foreach ($form_config['fields'] as $field):
                    if ($field['type'] === 'radio'):
                        echo render_radio_group($field['name'], $field['label'], $field['options'], $_POST[$field['name']] ?? $field['default'] ?? '', $field['required'] ?? false);
                    elseif ($field['type'] === 'number'):
                        echo render_number_input($field['name'], $field['label'], $_POST[$field['name']] ?? 0, $field['required'] ?? false, $field['min'] ?? 0, $field['step'] ?? '0.01', $field['placeholder'] ?? '0.00');
                    elseif ($field['type'] === 'select' && isset($field['depends_on'])):
                        $show_class = (isset($_POST[$field['depends_on']]) && $_POST[$field['depends_on']] === $field['show_when']) ? '' : 'hidden';
                        ?>
                        <div id="currency-field" class="<?= $show_class ?>">
                            <?= render_select_input($field['name'], $field['label'], get_lookup_options($field['options']), $_POST[$field['name']] ?? '', $field['required'] ?? false) ?>
                        </div>
                        <?php
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Program Measures -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Program Measures</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php 
                $category_registry = db_read('category_registry');
                $category_b = null;
                foreach ($category_registry['categories'] as $cat) {
                    if ($cat['id'] === 'category_b') {
                        $category_b = $cat;
                        break;
                    }
                }
                
                $allowed_programs = $category_b['programs_allowed'] ?? [];
                foreach ($program_registry['programs'] as $program): 
                    if (!in_array($program['key'], $allowed_programs)) continue;
                    
                    $step = $program['type'] === 'money' ? '0.01' : '1';
                    $placeholder = $program['type'] === 'money' ? '0.00' : '0';
                ?>
                    <?= render_number_input(
                        $program['key'], 
                        $program['label'] . ($program['type'] === 'count' ? ' (Count)' : ''), 
                        $_POST[$program['key']] ?? 0, 
                        false, 
                        0, 
                        $step, 
                        $placeholder
                    ) ?>
                <?php endforeach; ?>
            </div>
            
            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">Program Types</h4>
                <div class="text-sm text-blue-800 space-y-1">
                    <div><strong>Money Programs:</strong> Enter monetary amounts that will be normalized to global ESPEES</div>
                    <div><strong>Count Programs:</strong> Enter whole numbers (e.g., number of streets adopted, outreaches conducted)</div>
                </div>
            </div>
        </div>

        <!-- Submit Actions -->
        <div class="card p-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    <p>Your submission will be saved and approved immediately.</p>
                </div>
                <div class="flex space-x-4">
                    <button type="submit" class="btn-primary">
                        Submit Record
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Handle money denomination change
    $('input[name="money_denomination"]').on('change', function() {
        if ($(this).val() === 'ESPEES') {
            $('#currency-field').hide();
            $('#currency_code').prop('required', false);
        } else {
            $('#currency-field').show();
            $('#currency_code').prop('required', true);
        }
    });

    // Handle zone change to dynamically load groups
    $('select[name="zone"]').on('change', function() {
        const selectedZone = $(this).val();
        const groupSelect = $('select[name="group"]');

        if (selectedZone) {
            // Clear current group options and show loading
            groupSelect.empty().append('<option value="">Loading groups...</option>');

            // Use AJAX to get filtered groups
            $.get(window.location.pathname + '?r=submit_category_b&zone=' + encodeURIComponent(selectedZone) + '&ajax=groups', function(data) {
                // Parse the JSON response and update group dropdown
                if (data && data.groups) {
                    groupSelect.empty().append('<option value="">Select Group</option>');
                    $.each(data.groups, function(value, text) {
                        groupSelect.append('<option value="' + value + '">' + text + '</option>');
                    });
                }
            }).fail(function() {
                // Fallback: reload page if AJAX fails
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('zone', selectedZone);
                window.location.href = currentUrl.toString();
            });
        } else {
            // No zone selected, clear groups
            groupSelect.empty().append('<option value="">Select Zone first</option>');
        }
    });

    // Form validation
    $('#category-b-form').on('submit', function(e) {
        let isValid = true;
        let firstEmptyField = null;

        <?php
        $current_user = get_current_app_user();
        $is_rzm = isset($current_user['role']) && $current_user['role'] === 'RZM';
        ?>

        // Check essential required fields
        const fieldChecks = [
            { name: 'country', display: 'Country' },
            { name: 'money_denomination', display: 'Money Denomination' },
            { name: 'money_amount', display: 'Money Amount' }
            <?php if (!$is_rzm): ?>,
            { name: 'region_id', display: 'Region' },
            { name: 'zone_id', display: 'Zone' },
            { name: 'group', display: 'Group' }
            <?php endif; ?>
        ];

        fieldChecks.forEach(function(field) {
            const input = $('[name="' + field.name + '"]');
            const value = input.val();

            if (!value || value.trim() === '') {
                isValid = false;
                input.addClass('border-red-500').removeClass('border-green-500');
                if (!firstEmptyField) {
                    firstEmptyField = field.display;
                }
            } else {
                input.removeClass('border-red-500').addClass('border-green-500');
            }
        });

        if (!isValid) {
            e.preventDefault();
            if (firstEmptyField) {
                showToast('error', 'Please fill in: ' + firstEmptyField);
            }
            // Scroll to first empty field
            const firstError = $('.border-red-500').first();
            if (firstError.length) {
                firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    });

    // Real-time validation feedback
    $('input[required], select[required]').on('blur', function() {
        if ($(this).val()) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        } else {
            $(this).removeClass('border-green-500').addClass('border-red-500');
        }
    });
});

// Draft functionality removed - records are auto-approved
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
