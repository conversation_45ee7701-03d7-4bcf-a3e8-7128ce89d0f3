<?php
/**
 * Children Partners Submission Form
 * Children's ministry partner data submission form
 */

require_once __DIR__ . '/../../app/db.php';
require_once __DIR__ . '/../../controllers/records.php';
require_once __DIR__ . '/../components.php';

$page_title = 'Submit Children Partners';

// Load form configuration
$form_config = db_read('forms/partners_kids');
$program_registry = db_read('program_registry');

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = submit_record($_POST);

    if ($result['success']) {
        header('Location: ?r=dashboard&success=' . urlencode($result['message']));
        exit;
    } else {
        $errors = $result['errors'] ?? [];
        $error_message = $result['error'] ?? 'Submission failed';
    }
}

ob_start();
?>

<div class="max-w-4xl mx-auto space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Children Partners</h1>
            <p class="text-gray-600 mt-1">Submit data for children partner activities and sponsorships</p>
        </div>
        <a href="?r=dashboard" class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
        <?= htmlspecialchars($error_message) ?>
        <?php if (!empty($errors)): ?>
        <ul class="mt-2 list-disc list-inside">
            <?php foreach ($errors as $field => $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Main Form -->
    <form method="POST" class="space-y-6" id="kids-form">
        <?= csrf_token_input() ?>
        <input type="hidden" name="entity_type" value="Kids">

        <!-- Personal Information -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="title" class="form-label">Title</label>
                    <select id="title" name="title" class="form-select">
                        <option value="">Select Title</option>
                        <option value="Master">Master</option>
                        <option value="Miss">Miss</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <?= render_text_input('name', 'Full Name', $_POST['name'] ?? '', true, 'Enter child\'s full name') ?>

                <div>
                    <label for="age" class="form-label">Age</label>
                    <input type="number" id="age" name="age" class="form-input"
                           value="<?= htmlspecialchars($_POST['age'] ?? '') ?>"
                           min="1" max="18" placeholder="Enter age">
                </div>

                <?= render_text_input('kingschat_phone', 'KingsChat Phone', $_POST['kingschat_phone'] ?? '', true, 'Enter KingsChat phone number') ?>

                <?= render_text_input('email', 'Email', $_POST['email'] ?? '', true, 'Enter email address') ?>

                <div>
                    <label for="birthday" class="form-label">Birthday</label>
                    <input type="date" id="birthday" name="birthday" class="form-input"
                           value="<?= htmlspecialchars($_POST['birthday'] ?? '') ?>">
                </div>
            </div>
        </div>

                <!-- Location & Affiliation -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Location & Affiliation</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php
                $current_user = get_current_app_user();
                $is_admin = is_admin();
                $is_rzm = isset($current_user['role']) && $current_user['role'] === 'RZM';

                // Location & Affiliation fields only (exclude personal info fields)
                $location_fields = ['church', 'group', 'zone', 'region_id', 'country'];

                foreach ($form_config['fields'] as $field):
                    if (in_array($field['name'], $location_fields)):
                        if ($field['type'] === 'text'):
                            echo render_text_input($field['name'], $field['label'], $_POST[$field['name']] ?? '', $field['required'] ?? false, $field['placeholder'] ?? '');
                        elseif ($field['type'] === 'select'):
                            // Handle region/zone fields with auto-determination for RZM/Admin
                            if ($field['options'] === 'regions' || $field['options'] === 'zones') {
                                // Hide region and zone fields completely for RZM users
                                if (($field['options'] === 'regions' || $field['options'] === 'zones') && $is_rzm) {
                                    continue; // Don't show these fields at all for RZM
                                }

                                // Auto-determine region/zone for admin users only
                                $selected_value = $_POST[$field['name']] ?? '';
                                if (empty($selected_value) && $is_admin) {
                                    if ($field['options'] === 'regions' && isset($current_user['region_id'])) {
                                        $selected_value = $current_user['region_id'];
                                    } elseif ($field['options'] === 'zones' && isset($current_user['zone_id'])) {
                                        $selected_value = $current_user['zone_id'];
                                    }
                                }

                                // For admin users, make region/zone readonly and auto-selected
                                $readonly = $is_admin ? 'readonly' : '';
                                $required = $is_admin ? false : ($field['required'] ?? false);
                                $user_type = $is_admin ? 'Admin' : ($is_rzm ? 'RZM' : '');
                                $label_suffix = $is_admin ? " <span class=\"text-xs text-secondary-500\">(Auto-determined for {$user_type})</span>" : '';
                                ?>
                                <div class="mb-4">
                                    <label for="<?= $field['name'] ?>" class="form-label">
                                        <?= htmlspecialchars($field['label']) ?><?= $label_suffix ?>
                                        <?php if ($required): ?><span class="text-red-500">*</span><?php endif; ?>
                                    </label>
                                    <select id="<?= $field['name'] ?>" name="<?= $field['name'] ?>" class="form-select" <?= $readonly ?> <?= $required ? 'required' : '' ?>>
                                        <option value="">Select <?= htmlspecialchars($field['label']) ?></option>
                                        <?php foreach (get_lookup_options($field['options']) as $value => $text): ?>
                                            <option value="<?= htmlspecialchars($value) ?>" <?= $value == $selected_value ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($text) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php
                            } elseif ($field['options'] === 'groups') {
                                // Handle group field - make it optional and filter by zone
                                $selected_value = $_POST[$field['name']] ?? '';
                                ?>
                                <div class="mb-4">
                                    <label for="<?= $field['name'] ?>" class="form-label">
                                        <?= htmlspecialchars($field['label']) ?>
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <select id="<?= $field['name'] ?>" name="<?= $field['name'] ?>" class="form-select">
                                        <option value="">Select Group *</option>
                                        <?php foreach (get_lookup_options($field['options']) as $value => $text): ?>
                                            <option value="<?= htmlspecialchars($value) ?>" <?= $value == $selected_value ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($text) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php
                            } else {
                                echo render_select_input($field['name'], $field['label'], get_lookup_options($field['options']), $_POST[$field['name']] ?? '', $field['required'] ?? false);
                            }
                        endif;
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Kiddies Program Activities -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Kiddies Program Activities</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php
                $category_registry = db_read('category_registry');
                $kids_category = null;
                foreach ($category_registry['categories'] as $cat) {
                    if ($cat['id'] === 'partners_kids') {
                        $kids_category = $cat;
                        break;
                    }
                }

                $allowed_programs = $kids_category['programs_allowed'] ?? [];
                foreach ($program_registry['programs'] as $program):
                    if (!in_array($program['key'], $allowed_programs)) continue;

                    $step = $program['type'] === 'money' ? '0.01' : '1';
                    $placeholder = $program['type'] === 'money' ? '0.00' : '0';
                ?>
                    <div>
                        <label for="<?= $program['key'] ?>" class="form-label">
                            <?= htmlspecialchars($program['label']) ?>
                            <?php if ($program['type'] === 'count'): ?><span class="text-sm text-gray-500">(Count)</span><?php endif; ?>
                        </label>
                        <input type="number" id="<?= $program['key'] ?>" name="<?= $program['key'] ?>" class="form-input"
                               value="<?= htmlspecialchars($_POST[$program['key']] ?? 0) ?>"
                               min="0" step="<?= $step ?>" placeholder="<?= $placeholder ?>">
                        <?php if ($program['description']): ?>
                        <p class="text-xs text-gray-500 mt-1"><?= htmlspecialchars($program['description']) ?></p>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">Kiddies Program Types</h4>
                <div class="text-sm text-blue-800 space-y-1">
                    <div><strong>Total Kiddies ROR:</strong> Total number of kiddies participating in ROR programs</div>
                    <div><strong>Other ROR Projects:</strong> Additional ROR initiatives for children</div>
                    <div><strong>Grand Total:</strong> Overall total for all kiddies programs</div>
                </div>
            </div>
        </div>

        <!-- Submit Actions -->
        <div class="card p-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    <p>Your submission will be saved and approved immediately.</p>
                </div>
                <div class="flex space-x-4">
                    <button type="submit" class="btn-primary">
                        Submit Record
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Handle zone change to dynamically load groups
    $('select[name="zone"]').on('change', function() {
        const selectedZone = $(this).val();
        const groupSelect = $('select[name="group"]');

        if (selectedZone) {
            // Get the zone name from the selected option text
            const selectedZoneText = $('select[name="zone"] option:selected').text();

            // Clear current group options and show loading
            groupSelect.empty().append('<option value="">Loading groups...</option>');

            // Use AJAX to get filtered groups
            $.get(window.location.pathname + '?r=submit_kids&zone=' + encodeURIComponent(selectedZoneText) + '&ajax=groups', function(data) {
                // Parse the JSON response and update group dropdown
                if (data && data.groups) {
                    groupSelect.empty().append('<option value="">Select Group *</option>');
                    $.each(data.groups, function(value, text) {
                        groupSelect.append('<option value="' + value + '">' + text + '</option>');
                    });
                }
            }).fail(function() {
                // Fallback: reload page if AJAX fails
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('zone', selectedZoneText);
                window.location.href = currentUrl.toString();
            });
        } else {
            // No zone selected, clear groups
            groupSelect.empty().append('<option value="">Select Zone first</option>');
        }
    });

    // Form validation
    $('#kids-form').on('submit', function(e) {
        let isValid = true;
        const requiredFields = [
            'name', 'kingschat_phone', 'email', 'group', 'country'
            <?php
            $current_user = get_current_app_user();
            $is_admin = is_admin();
            $is_rzm = isset($current_user['role']) && $current_user['role'] === 'RZM';

            // Exclude region/zone fields from validation for RZM users since they're auto-determined
            if (!$is_rzm) {
                echo ", 'zone', 'region_id'";
            }
            ?>
        ];

        requiredFields.forEach(function(field) {
            const input = $('[name="' + field + '"]');
            if (!input.val()) {
                isValid = false;
                input.addClass('border-red-500');
                showToast('error', 'Please fill in all required fields');
            } else {
                input.removeClass('border-red-500');
            }
        });

        if (!isValid) {
            e.preventDefault();
        }
    });

    // Real-time validation feedback
    $('input[required], select[required]').on('blur', function() {
        if ($(this).val()) {
            $(this).removeClass('border-red-500').addClass('border-green-500');
        } else {
            $(this).removeClass('border-green-500').addClass('border-red-500');
        }
    });

    // Age validation
    $('#age').on('change', function() {
        const age = parseInt($(this).val());
        if (age < 1 || age > 18) {
            $(this).addClass('border-red-500');
            showToast('warning', 'Age should be between 1 and 18');
        } else {
            $(this).removeClass('border-red-500');
        }
    });
});

// Draft functionality removed - records are auto-approved
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
