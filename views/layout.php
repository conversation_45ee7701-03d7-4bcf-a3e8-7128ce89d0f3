<?php
/**
 * Main Layout Template
 * Provides the base HTML structure with Tailwind CDN and other assets
 */

require_once __DIR__ . '/../app/auth.php';
$current_user = get_current_app_user();
$is_admin = is_admin();
$page_title = $page_title ?? 'IPPC Awards Portal';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Tailwind CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554'
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617'
                        },
                        accent: {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                            950: '#4a044e'
                        },
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
                        display: ['Poppins', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif']
                    },
                    boxShadow: {
                        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
                        'medium': '0 4px 25px -2px rgba(0, 0, 0, 0.1), 0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                        'glow': '0 0 20px rgba(59, 130, 246, 0.15)',
                        'glow-accent': '0 0 20px rgba(217, 70, 239, 0.15)'
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'bounce-soft': 'bounceSoft 0.6s ease-out'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        bounceSoft: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- DataTables (requires jQuery) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <link href="https://cdn.datatables.net/1.13.8/css/jquery.dataTables.min.css" rel="stylesheet">
    <script src="https://cdn.datatables.net/1.13.8/js/jquery.dataTables.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom styles -->
    <style>
        /* Enhanced Card Styles */
        .card {
            @apply bg-white rounded-2xl shadow-soft border border-secondary-200/50 backdrop-blur-sm;
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.8) 100%);
        }
        .card:hover {
            @apply shadow-medium;
            transform: translateY(-2px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Enhanced Button Styles */
        .btn-primary {
            @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-soft hover:shadow-glow;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background-size: 200% 200%;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            background-position: right center;
        }

        .btn-secondary {
            @apply bg-gradient-to-r from-secondary-100 to-secondary-200 hover:from-secondary-200 hover:to-secondary-300 text-secondary-700 font-medium py-3 px-6 rounded-xl shadow-soft border border-secondary-300/50;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .btn-secondary:hover {
            @apply shadow-medium;
            transform: translateY(-1px);
        }

        .btn-danger {
            @apply bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium py-3 px-6 rounded-xl shadow-soft;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .btn-danger:hover {
            @apply shadow-medium;
            transform: translateY(-1px);
        }

        .btn-success {
            @apply bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium py-3 px-6 rounded-xl shadow-soft;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .btn-success:hover {
            @apply shadow-medium;
            transform: translateY(-1px);
        }

        /* Enhanced Form Styles */
        .form-input {
            @apply block w-full px-4 py-3 border border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 bg-secondary-50/50;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .form-input:focus {
            @apply bg-white shadow-glow;
            transform: translateY(-1px);
        }

        .form-select {
            @apply block w-full px-4 py-3 border border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 bg-secondary-50/50;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .form-select:focus {
            @apply bg-white shadow-glow;
            transform: translateY(-1px);
        }

        .form-label {
            @apply block text-sm font-semibold text-secondary-700 mb-2;
            background: linear-gradient(135deg, #475569 0%, #64748b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Page Header Enhancement */
        .page-header {
            @apply bg-gradient-to-r from-primary-50 via-accent-50 to-primary-100 border-b border-primary-200/50;
            background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 75% 75%, rgba(217, 70, 239, 0.1) 0%, transparent 50%);
        }

        /* Enhanced Navigation */
        .nav-link {
            @apply relative px-4 py-2 rounded-xl font-medium transition-all duration-300;
        }
        .nav-link:hover {
            @apply bg-primary-50 text-primary-700;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }
        .nav-link.active {
            @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-glow;
        }

        /* Status Badges */
        .badge-admin {
            @apply px-3 py-1 text-xs font-bold bg-gradient-to-r from-accent-500 to-accent-600 text-white rounded-full shadow-soft;
        }
        .badge-rzm {
            @apply px-3 py-1 text-xs font-bold bg-gradient-to-r from-success to-green-600 text-white rounded-full shadow-soft;
        }

        /* DataTables Enhancement */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            @apply text-sm text-secondary-600 font-medium;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            @apply px-4 py-2 mx-1 rounded-xl border border-secondary-300 hover:bg-primary-50 hover:border-primary-300 transition-all duration-200;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white border-primary-500 shadow-soft;
        }

        /* Enhanced Table Styling */
        .dataTable {
            @apply rounded-xl overflow-hidden shadow-soft bg-white;
        }
        .dataTable thead th {
            @apply bg-gradient-to-r from-primary-50 via-primary-100/50 to-accent-50 text-primary-800 font-bold py-5 px-6 border-b-2 border-primary-200/30;
        }
        .dataTable tbody tr {
            @apply hover:bg-gradient-to-r hover:from-primary-50/30 hover:to-accent-50/30 transition-all duration-200 cursor-pointer;
        }
        .dataTable tbody tr:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .dataTable tbody td {
            @apply py-4 px-6 border-b border-secondary-100/50;
        }

        /* Custom DataTable styling */
        #records-table_wrapper {
            @apply bg-transparent;
        }
        #records-table_wrapper .dataTables_info {
            @apply text-sm text-secondary-600 font-medium;
        }
        #records-table_wrapper .dataTables_paginate {
            @apply text-sm;
        }
        #records-table_wrapper .dataTables_paginate .paginate_button {
            @apply px-4 py-2 mx-1 rounded-xl border border-secondary-300 hover:bg-primary-50 hover:border-primary-300 transition-all duration-200;
        }
        #records-table_wrapper .dataTables_paginate .paginate_button.current {
            @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white border-primary-500 shadow-soft;
        }
        #records-table_wrapper .dataTables_length select {
            @apply px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200;
        }

        /* DataTable specific enhancements */
        #records-table_wrapper .dataTables_filter input {
            @apply pl-10 pr-4 py-3 border border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 bg-secondary-50/50 transition-all duration-200 text-sm w-80;
        }
        #records-table_wrapper .dataTables_filter {
            @apply relative;
        }
        #records-table_wrapper .dataTables_filter::before {
            content: '';
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%236b7280' viewBox='0 0 24 24'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-size: contain;
        }

        /* Table responsive enhancements */
        @media (max-width: 1024px) {
            #records-table_wrapper .dataTables_filter input {
                @apply w-full;
            }
            .dataTable tbody td {
                @apply px-3 py-3 text-sm;
            }
            .dataTable thead th {
                @apply px-3 py-4 text-xs;
            }
        }

        @media (max-width: 768px) {
            .dataTable {
                @apply text-xs;
            }
            .dataTable thead th {
                @apply px-2 py-3;
            }
            .dataTable tbody td {
                @apply px-2 py-2;
            }
            #records-table_wrapper .dataTables_filter input {
                @apply text-xs py-2;
            }
        }

        /* Animation Classes */
        .animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
        .animate-slide-up { animation: slideUp 0.3s ease-out; }
        .animate-bounce-soft { animation: bounceSoft 0.6s ease-out; }

        /* Responsive Design Enhancements */
        @media (max-width: 768px) {
            .card { @apply mx-4; }
            .btn-primary, .btn-secondary { @apply w-full; }
        }

        /* Loading States */
        .loading {
            @apply opacity-50 pointer-events-none;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Focus Styles for Accessibility */
        .form-input:focus-visible,
        .form-select:focus-visible,
        .btn-primary:focus-visible,
        .btn-secondary:focus-visible {
            @apply outline-none ring-2 ring-primary-500 ring-offset-2;
        }

        /* Dropdown Arrow Animation */
        .rotate-180 {
            transform: rotate(180deg);
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-secondary-50 via-white to-primary-50/30 font-sans min-h-screen animate-fade-in">
    <!-- Navigation -->
    <?php if ($current_user): ?>
    <nav class="bg-white/80 backdrop-blur-md shadow-soft border-b border-secondary-200/50 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo/Brand -->
                    <div class="flex-shrink-0 flex items-center mr-8">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <h1 class="text-xl font-bold bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">IPPC Awards Portal</h1>
                        </div>
                    </div>

                    <!-- Navigation Links -->
                    <div class="hidden sm:flex sm:space-x-2">
                        <a href="?r=dashboard" class="nav-link <?= ($_GET['r'] ?? 'dashboard') === 'dashboard' ? 'active' : '' ?>">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                            </svg>
                            Dashboard
                        </a>

                        <a href="?r=profile" class="nav-link <?= ($_GET['r'] ?? '') === 'profile' ? 'active' : '' ?>">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            My Profile
                        </a>

                        <!-- Submit Data Dropdown -->
                        <div class="relative inline-block text-left">
                            <button type="button" class="nav-link" id="submit-menu-button">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Submit Data
                                <svg class="ml-2 h-4 w-4 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div class="hidden absolute left-0 mt-3 w-64 rounded-2xl shadow-medium bg-white/95 backdrop-blur-md border border-secondary-200/50 overflow-hidden" id="submit-menu">
                                <div class="py-2">
                                    <div class="px-4 py-2 text-xs font-semibold text-secondary-500 uppercase tracking-wide">Data Categories</div>
                                    <a href="?r=submit_campaigns" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-accent-500 rounded-full mr-3"></div>
                                        Campaigns & Sub-Campaigns
                                    </a>

                                    <a href="?r=submit_category_b" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-success rounded-full mr-3"></div>
                                        Category B - Group Churches
                                    </a>
                                    <a href="?r=submit_category_c" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-warning rounded-full mr-3"></div>
                                        Category C - Local Churches
                                    </a>
                                    <a href="?r=submit_cells" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-accent-500 rounded-full mr-3"></div>
                                        Cells
                                    </a>
                                    <div class="border-t border-secondary-200/50 my-2"></div>
                                    <div class="px-4 py-2 text-xs font-semibold text-secondary-500 uppercase tracking-wide">Partners</div>
                                    <a href="?r=submit_adult" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-primary-500 rounded-full mr-3"></div>
                                        Adult Partners
                                    </a>
                                    <a href="?r=submit_kids" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-accent-500 rounded-full mr-3"></div>
                                        Children Partners
                                    </a>
                                    <a href="?r=submit_teens" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-warning rounded-full mr-3"></div>
                                        Teen Partners
                                    </a>
                                    <a href="?r=submit_external" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <div class="w-2 h-2 bg-success rounded-full mr-3"></div>
                                        External Partners
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Admin Dropdown -->
                        <?php if ($is_admin): ?>
                        <div class="relative inline-block text-left">
                            <button type="button" class="nav-link" id="admin-menu-button">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Admin
                                <svg class="ml-2 h-4 w-4 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div class="hidden absolute left-0 mt-3 w-56 rounded-2xl shadow-medium bg-white/95 backdrop-blur-md border border-secondary-200/50 overflow-hidden" id="admin-menu">
                                <div class="py-2">
                                    <a href="?r=admin_users" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                        User Management
                                    </a>

                                    <a href="?r=admin_settings" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        Settings & Registries
                                    </a>
                                    <a href="?r=admin_stats" class="flex items-center px-4 py-3 text-sm text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                        System Statistics
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- User Profile Section -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="text-right">
                            <div class="text-sm font-medium text-secondary-900">
                                <?= htmlspecialchars($current_user['name'] ?? $current_user['email']) ?>
                            </div>
                            <div class="text-xs text-secondary-500">
                                Welcome back!
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">
                                    <?= strtoupper(substr($current_user['name'] ?? $current_user['email'], 0, 1)) ?>
                                </span>
                            </div>
                        </div>
                        <?php if ($is_admin): ?>
                            <span class="badge-admin">Admin</span>
                        <?php else: ?>
                            <span class="badge-rzm">RZM</span>
                        <?php endif; ?>
                    </div>

                    <div class="border-l border-secondary-200 pl-4">
                        <button onclick="confirmAction('Are you sure you want to logout?', 'Logout Confirmation').then(result => { if(result.isConfirmed) window.location.href='?r=logout'; })"
                                class="text-secondary-400 hover:text-secondary-600 p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200">
                            <span class="sr-only">Logout</span>
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="<?= $current_user ? 'max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 animate-slide-up' : '' ?>">
        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php endif; ?>
    </main>

    <!-- Scripts -->
    <script>
        // Enhanced dropdown menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const menuButtons = ['submit-menu-button', 'admin-menu-button'];

            menuButtons.forEach(buttonId => {
                const button = document.getElementById(buttonId);
                const menu = document.getElementById(buttonId.replace('-button', ''));
                const arrow = button?.querySelector('svg:last-child');

                if (button && menu && arrow) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();

                        // Close other menus first
                        menuButtons.forEach(otherId => {
                            if (otherId !== buttonId) {
                                const otherButton = document.getElementById(otherId);
                                const otherMenu = document.getElementById(otherId.replace('-button', ''));
                                const otherArrow = otherButton?.querySelector('svg:last-child');

                                if (otherMenu && !otherMenu.classList.contains('hidden')) {
                                    otherMenu.classList.add('hidden');
                                    otherArrow?.classList.remove('rotate-180');
                                }
                            }
                        });

                        // Toggle current menu
                        const isHidden = menu.classList.contains('hidden');
                        menu.classList.toggle('hidden');

                        // Animate arrow
                        if (arrow) {
                            if (isHidden) {
                                arrow.classList.add('rotate-180');
                            } else {
                                arrow.classList.remove('rotate-180');
                            }
                        }

                        // Add entrance animation
                        if (isHidden) {
                            menu.style.opacity = '0';
                            menu.style.transform = 'translateY(-10px)';
                            requestAnimationFrame(() => {
                                menu.style.transition = 'all 0.2s ease-out';
                                menu.style.opacity = '1';
                                menu.style.transform = 'translateY(0)';
                            });
                        }
                    });

                    // Close menu when clicking outside
                    document.addEventListener('click', function(e) {
                        if (!button.contains(e.target) && !menu.contains(e.target)) {
                            menu.classList.add('hidden');
                            arrow?.classList.remove('rotate-180');
                        }
                    });
                }
            });

            // Add smooth animations to navigation links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add loading states to buttons
            const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .btn-danger, .btn-success');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.classList.contains('loading')) {
                        this.classList.add('loading');
                        const originalText = this.innerHTML;
                        setTimeout(() => {
                            this.classList.remove('loading');
                        }, 2000); // Remove loading state after 2 seconds
                    }
                });
            });
        });
        
        // Global CSRF token for AJAX requests
        window.csrfToken = '<?= generate_csrf_token() ?>';
        
        // Global toast function
        window.showToast = function(type, message, title = '') {
            const icons = {
                success: 'success',
                error: 'error',
                warning: 'warning',
                info: 'info'
            };
            
            Swal.fire({
                icon: icons[type] || 'info',
                title: title || (type.charAt(0).toUpperCase() + type.slice(1)),
                text: message,
                toast: true,
                position: 'top-end',
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        };
        
        // Global confirm dialog
        window.confirmAction = function(message, title = 'Confirm Action') {
            return Swal.fire({
                title: title,
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, proceed',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#0ea5e9',
                cancelButtonColor: '#6b7280'
            });
        };
    </script>
</body>
</html>
