<?php
/**
 * Reusable UI Components
 * Provides common form inputs, filters, and other UI elements
 */

function render_text_input($name, $label, $value = '', $required = false, $placeholder = '', $type = 'text') {
    $required_attr = $required ? 'required' : '';
    $required_mark = $required ? '<span class="text-red-500">*</span>' : '';
    
    return "
    <div class='mb-4'>
        <label for='{$name}' class='form-label'>{$label} {$required_mark}</label>
        <input type='{$type}' id='{$name}' name='{$name}' value='" . htmlspecialchars($value) . "' 
               placeholder='" . htmlspecialchars($placeholder) . "' class='form-input' {$required_attr}>
    </div>";
}

function render_select_input($name, $label, $options, $selected = '', $required = false, $placeholder = 'Select an option') {
    $required_attr = $required ? 'required' : '';
    $required_mark = $required ? '<span class="text-red-500">*</span>' : '';
    
    $html = "
    <div class='mb-4'>
        <label for='{$name}' class='form-label'>{$label} {$required_mark}</label>
        <select id='{$name}' name='{$name}' class='form-select' {$required_attr}>
            <option value=''>{$placeholder}</option>";
    
    foreach ($options as $value => $text) {
        $selected_attr = $value == $selected ? 'selected' : '';
        $html .= "<option value='" . htmlspecialchars($value) . "' {$selected_attr}>" . htmlspecialchars($text) . "</option>";
    }
    
    $html .= "
        </select>
    </div>";
    
    return $html;
}

function render_number_input($name, $label, $value = 0, $required = false, $min = 0, $step = '0.01', $placeholder = '0.00') {
    $required_attr = $required ? 'required' : '';
    $required_mark = $required ? '<span class="text-red-500">*</span>' : '';
    
    return "
    <div class='mb-4'>
        <label for='{$name}' class='form-label'>{$label} {$required_mark}</label>
        <input type='number' id='{$name}' name='{$name}' value='{$value}' 
               min='{$min}' step='{$step}' placeholder='{$placeholder}' 
               class='form-input' {$required_attr}>
    </div>";
}

function render_radio_group($name, $label, $options, $selected = '', $required = false) {
    $required_attr = $required ? 'required' : '';
    $required_mark = $required ? '<span class="text-red-500">*</span>' : '';
    
    $html = "
    <div class='mb-4'>
        <label class='form-label'>{$label} {$required_mark}</label>
        <div class='mt-2 space-y-2'>";
    
    foreach ($options as $option) {
        $value = $option['value'];
        $text = $option['label'];
        $checked = $value == $selected ? 'checked' : '';
        
        $html .= "
        <div class='flex items-center'>
            <input type='radio' id='{$name}_{$value}' name='{$name}' value='{$value}' 
                   class='h-4 w-4 text-primary focus:ring-primary border-gray-300' {$checked} {$required_attr}>
            <label for='{$name}_{$value}' class='ml-2 text-sm text-gray-700'>{$text}</label>
        </div>";
    }
    
    $html .= "
        </div>
    </div>";
    
    return $html;
}

function render_month_input($name, $label, $value = '', $required = false) {
    $required_attr = $required ? 'required' : '';
    $required_mark = $required ? '<span class="text-red-500">*</span>' : '';
    
    // Default to current month if no value provided
    if (!$value) {
        $value = date('Y-m');
    }
    
    return "
    <div class='mb-4'>
        <label for='{$name}' class='form-label'>{$label} {$required_mark}</label>
        <input type='month' id='{$name}' name='{$name}' value='{$value}' 
               class='form-input' {$required_attr}>
    </div>";
}

function render_card($title, $content, $actions = '') {
    return "
    <div class='card p-6'>
        <div class='flex justify-between items-center mb-4'>
            <h3 class='text-lg font-semibold text-gray-900'>{$title}</h3>
            {$actions}
        </div>
        {$content}
    </div>";
}

function render_kpi_card($title, $value, $subtitle = '', $trend = null) {
    $trend_html = '';
    if ($trend) {
        $trend_color = $trend['direction'] === 'up' ? 'text-green-600' : 'text-red-600';
        $trend_icon = $trend['direction'] === 'up' ? '↑' : '↓';
        $trend_html = "<div class='text-sm {$trend_color}'>{$trend_icon} {$trend['value']}</div>";
    }
    
    return "
    <div class='card p-6'>
        <div class='text-sm font-medium text-gray-500 uppercase tracking-wide'>{$title}</div>
        <div class='mt-2 text-3xl font-bold text-gray-900'>{$value}</div>
        {$subtitle}
        {$trend_html}
    </div>";
}

function render_table($id, $columns, $data = [], $options = []) {
    $table_class = $options['class'] ?? 'display w-full';
    
    $html = "<table id='{$id}' class='{$table_class}'><thead><tr>";
    
    foreach ($columns as $column) {
        $html .= "<th>" . htmlspecialchars($column['title']) . "</th>";
    }
    
    $html .= "</tr></thead><tbody>";
    
    foreach ($data as $row) {
        $html .= "<tr>";
        foreach ($columns as $column) {
            $value = $row[$column['data']] ?? '';
            if (isset($column['render']) && is_callable($column['render'])) {
                $value = $column['render']($value, $row);
            }
            $html .= "<td>" . htmlspecialchars($value) . "</td>";
        }
        $html .= "</tr>";
    }
    
    $html .= "</tbody></table>";
    
    return $html;
}

function render_filter_panel($filters) {
    $html = "
    <div class='card p-6 mb-6'>
        <h3 class='text-lg font-semibold text-gray-900 mb-4'>Filters</h3>
        <form id='filter-form' class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>";
    
    foreach ($filters as $filter) {
        switch ($filter['type']) {
            case 'text':
                $html .= render_text_input($filter['name'], $filter['label'], '', false, $filter['placeholder'] ?? '');
                break;
            case 'select':
                $html .= render_select_input($filter['name'], $filter['label'], $filter['options'], '', false, $filter['placeholder'] ?? 'All');
                break;
            case 'month':
                $html .= render_month_input($filter['name'], $filter['label']);
                break;
        }
    }
    
    $html .= "
            <div class='flex items-end'>
                <button type='submit' class='btn-primary mr-2'>Apply Filters</button>
                <button type='button' id='clear-filters' class='btn-secondary'>Clear</button>
            </div>
        </form>
    </div>";
    
    return $html;
}



// Helper function to get lookup options
function get_lookup_options($type) {
    require_once __DIR__ . '/../app/db.php';
    require_once __DIR__ . '/../app/normalize.php';
    require_once __DIR__ . '/../app/access.php';
    
    switch ($type) {
        case 'regions':
            return array_combine(
                get_user_regions(),
                array_map(fn($r) => "Region {$r}", get_user_regions())
            );
            
        case 'zones':
            // Read zones from zones.json file
            $zones_data = json_decode(file_get_contents(__DIR__ . '/../zones.json'), true);
            $zones = [];

            if (is_admin()) {
                // Admin sees all zones
                foreach ($zones_data as $region => $region_data) {
                    if (isset($region_data) && is_array($region_data)) {
                        foreach ($region_data as $zone_name => $zone_data) {
                            if (isset($zone_data['name'])) {
                                $zones[$zone_name] = $zone_data['name'];
                            } else {
                                $zones[$zone_name] = $zone_name;
                            }
                        }
                    }
                }
            } else {
                // RZM users only see their assigned zone
                require_once __DIR__ . '/../app/auth.php';
                $current_user = get_current_app_user();

                if ($current_user && isset($current_user['role']) && $current_user['role'] === 'RZM' && isset($current_user['zone_id'])) {
                    $rzm_zone = $current_user['zone_id'];
                    foreach ($zones_data as $region => $region_data) {
                        if (isset($region_data) && is_array($region_data)) {
                            foreach ($region_data as $zone_name => $zone_data) {
                                if ($zone_name === $rzm_zone) {
                                    if (isset($zone_data['name'])) {
                                        $zones[$zone_name] = $zone_data['name'];
                                    } else {
                                        $zones[$zone_name] = $zone_name;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Sort zones alphabetically
            asort($zones);
            return $zones;
            
        case 'countries':
            return get_country_options();
            
        case 'currencies':
            return get_currency_options();
            
        case 'entity_types':
            return [
                'Campaigns' => 'Campaigns',
                'CategoryB' => 'Category B',
                'CategoryC' => 'Category C',
                'Cell' => 'Cells',
                'Adult' => 'Partners - Adult',
                'Kids' => 'Partners - Kids',
                'Teens' => 'Partners - Teens',
                'External' => 'Partners - External'
            ];
            
        case 'groups':
            // Check if zone is specified via GET parameter
            $selected_zone = $_GET['zone'] ?? null;

            // Read groups from zones.json file
            $zones_data = json_decode(file_get_contents(__DIR__ . '/../zones.json'), true);
            $groups = [];

            if ($selected_zone) {
                // Filter groups by selected zone
                foreach ($zones_data as $region => $region_data) {
                    if (isset($region_data) && is_array($region_data)) {
                        foreach ($region_data as $zone_name => $zone_data) {
                            if ($zone_name === $selected_zone && isset($zone_data['groups']) && is_array($zone_data['groups'])) {
                                foreach ($zone_data['groups'] as $group) {
                                    $group_id = $group['id'] ?? $group['name'];
                                    $groups[$group_id] = $group['name'];
                                }
                            }
                        }
                    }
                }
            } else {
                // Check if current user is RZM and filter by their zone
                require_once __DIR__ . '/../app/auth.php';
                $current_user = get_current_app_user();

                if ($current_user && isset($current_user['role']) && $current_user['role'] === 'RZM' && isset($current_user['zone_id'])) {
                    // Filter groups by RZM's assigned zone
                    $rzm_zone = $current_user['zone_id'];
                    foreach ($zones_data as $region => $region_data) {
                        if (isset($region_data) && is_array($region_data)) {
                            foreach ($region_data as $zone_name => $zone_data) {
                                if ($zone_name === $rzm_zone && isset($zone_data['groups']) && is_array($zone_data['groups'])) {
                                    foreach ($zone_data['groups'] as $group) {
                                        $group_id = $group['id'] ?? $group['name'];
                                        $groups[$group_id] = $group['name'];
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // If no zone selected and not RZM, show all groups (fallback for admin users)
                    foreach ($zones_data as $region => $region_data) {
                        if (isset($region_data) && is_array($region_data)) {
                            foreach ($region_data as $zone_name => $zone_data) {
                                if (isset($zone_data['groups']) && is_array($zone_data['groups'])) {
                                    foreach ($zone_data['groups'] as $group) {
                                        $group_id = $group['id'] ?? $group['name'];
                                        $groups[$group_id] = $group['name'];
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Sort groups alphabetically
            asort($groups);
            return $groups;


            
        default:
            return [];
    }
}
