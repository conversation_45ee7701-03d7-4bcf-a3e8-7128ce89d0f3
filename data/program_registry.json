{"programs": [{"key": "WonderChallenge", "label": "Wonder Challenge", "type": "money", "enabled": true, "description": "Wonder Challenge program funding"}, {"key": "Languages", "label": "Languages", "type": "money", "enabled": true, "description": "Language translation and distribution"}, {"key": "Kiddies", "label": "Kiddies", "type": "money", "enabled": true, "description": "Children's ministry programs"}, {"key": "Teevo", "label": "Teevo", "type": "money", "enabled": true, "description": "Teen ministry programs"}, {"key": "NOLB", "label": "NOLB", "type": "money", "enabled": true, "description": "NOLB program funding"}, {"key": "Braille", "label": "Braille", "type": "money", "enabled": true, "description": "Braille materials production"}, {"key": "YouthAglow", "label": "Youth Aglow", "type": "money", "enabled": true, "description": "Youth ministry programs"}, {"key": "LocalDistribution", "label": "Local Distribution", "type": "money", "enabled": true, "description": "Local materials distribution"}, {"key": "Subscriptions_QUBADS", "label": "Subscriptions QUBADS", "type": "money", "enabled": true, "description": "QUBADS subscription services"}, {"key": "OtherProjects", "label": "Other Projects", "type": "money", "enabled": true, "description": "Miscellaneous ministry projects"}, {"key": "AdoptAStreet", "label": "Adopt A Street", "type": "count", "enabled": true, "description": "Street adoption program (count of streets)"}, {"key": "TotalOutreaches", "label": "Total Outreaches", "type": "count", "enabled": true, "description": "Total number of outreach events"}, {"key": "TotalQuantitySponsored", "label": "Total Quantity Sponsored", "type": "count", "enabled": true, "description": "Total items/people sponsored"}, {"key": "TotalAmountReceived", "label": "Total Amount Received", "type": "money", "enabled": true, "description": "Total monetary receipts"}, {"key": "TotalGiven", "label": "Total Given", "type": "money", "enabled": true, "description": "Total amount given"}, {"key": "GrandTotal", "label": "Grand Total", "type": "money", "enabled": true, "description": "Grand total of all giving"}, {"key": "TotalKiddiesROR", "label": "Total Kiddies ROR", "type": "count", "enabled": true, "description": "Total kiddies ROR count"}, {"key": "OtherRORProjects", "label": "Other ROR Projects", "type": "money", "enabled": true, "description": "Other ROR projects funding"}, {"key": "MonthlyTeevo", "label": "Monthly Teevo", "type": "money", "enabled": true, "description": "Monthly Teevo subscription"}, {"key": "ReachOutWorld", "label": "Reach Out World", "type": "count", "enabled": true, "description": "Reach Out World outreaches"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "count", "enabled": true, "description": "Rhapathon outreaches"}, {"key": "SayYesToKids", "label": "Say Yes to Kids", "type": "count", "enabled": true, "description": "Say Yes to Kids outreaches"}, {"key": "Teevolution", "label": "Teevolution", "type": "count", "enabled": true, "description": "Teevolution outreaches"}, {"key": "RhapsodySubscriptions", "label": "Rhapsody Subscriptions", "type": "count", "enabled": true, "description": "Rhapsody subscriptions"}, {"key": "QUBADS", "label": "QUBADS", "type": "count", "enabled": true, "description": "QUBADS sponsorships"}, {"key": "SponsorshipCallCentre", "label": "Sponsorship Call Centre", "type": "count", "enabled": true, "description": "Sponsorship through call centre"}, {"key": "QuantitySponsoredREON", "label": "Quantity Sponsored REON", "type": "count", "enabled": true, "description": "Quantity sponsored through REON"}, {"key": "TranslatorsNetwork", "label": "Translators Network International", "type": "count", "enabled": true, "description": "Translators Network International"}, {"key": "RIN", "label": "Rhapsody Influencers Network", "type": "count", "enabled": true, "description": "Rhapsody Influencers Network"}, {"key": "RIM", "label": "Rhapsody International Missions", "type": "count", "enabled": true, "description": "Rhapsody International Missions"}, {"key": "Total", "label": "Total", "type": "count", "enabled": true, "description": "Total count"}], "last_updated": "2025-01-01T00:00:00Z", "version": "1.0"}