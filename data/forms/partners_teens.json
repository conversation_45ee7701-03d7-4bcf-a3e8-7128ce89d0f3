{"title": "Teen Partners Submission", "description": "Submit data for teen partner activities and subscriptions", "entity_type": "Teens", "fields": [{"name": "zone", "label": "Zone", "type": "select", "required": false, "options": "zones"}, {"name": "title", "label": "Title", "type": "select", "required": false, "options": ["Mr", "Miss", "Ms", "Other"]}, {"name": "name", "label": "Name", "type": "text", "required": true, "placeholder": "Enter teen's full name"}, {"name": "kingschat_phone", "label": "KingsChat Phone", "type": "tel", "required": true, "placeholder": "Enter KingsChat phone number"}, {"name": "email", "label": "Email", "type": "email", "required": true, "placeholder": "Enter email address"}, {"name": "birthday", "label": "Birthday", "type": "date", "required": false, "placeholder": "Enter birthday"}, {"name": "church", "label": "Church", "type": "text", "required": false, "placeholder": "Enter church name (optional)"}, {"name": "group", "label": "Group", "type": "select", "required": true, "options": "groups"}, {"name": "region_id", "label": "Region", "type": "select", "required": false, "options": "regions"}, {"name": "country", "label": "Country", "type": "select", "required": true, "options": "countries"}], "program_fields": [{"name": "MonthlyTeevo", "label": "Monthly Teevo", "type": "number", "min": 0, "step": "0.01"}, {"name": "OtherRORProjects", "label": "Other ROR Projects", "type": "number", "min": 0, "step": "0.01"}, {"name": "GrandTotal", "label": "Grand Total", "type": "number", "min": 0, "step": "0.01"}]}