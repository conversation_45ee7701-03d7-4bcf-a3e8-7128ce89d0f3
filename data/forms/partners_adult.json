{"title": "Adult Partners Submission", "description": "Submit data for adult partner contributions and activities", "entity_type": "Adult", "fields": [{"name": "title", "label": "Title", "type": "select", "required": false, "options": ["Mr", "Mrs", "Ms", "Dr", "Prof", "Rev", "Pastor", "<PERSON>", "Other"]}, {"name": "name", "label": "Name", "type": "text", "required": true, "placeholder": "Enter full name"}, {"name": "kingschat_phone", "label": "KingsChat Phone", "type": "tel", "required": true, "placeholder": "Enter KingsChat phone number"}, {"name": "email", "label": "Email", "type": "email", "required": true, "placeholder": "Enter email address"}, {"name": "church", "label": "Church", "type": "text", "required": false, "placeholder": "Enter church name (optional)"}, {"name": "group", "label": "Group", "type": "select", "required": true, "options": "groups"}, {"name": "zone", "label": "Zone", "type": "select", "required": false, "options": "zones"}, {"name": "region_id", "label": "Region", "type": "select", "required": false, "options": "regions"}, {"name": "country", "label": "Country", "type": "select", "required": true, "options": "countries"}, {"name": "money_denomination", "label": "Money Denomination", "type": "radio", "required": true, "options": [{"value": "CURRENCY", "label": "Local Currency"}, {"value": "ESPEES", "label": "ESPEES"}]}, {"name": "money_amount", "label": "Total Amount Received", "type": "number", "required": true, "min": 0, "step": "0.01", "placeholder": "0.00"}, {"name": "currency_code", "label": "Currency Code", "type": "select", "required": false, "options": "currencies", "depends_on": "money_denomination", "show_when": "CURRENCY"}], "program_fields": [{"name": "WonderChallenge", "label": "Wonder Challenge", "type": "number", "min": 0, "step": "0.01"}, {"name": "Languages", "label": "Languages", "type": "number", "min": 0, "step": "0.01"}, {"name": "Kiddies", "label": "Kiddies", "type": "number", "min": 0, "step": "0.01"}, {"name": "Teevo", "label": "Teevo", "type": "number", "min": 0, "step": "0.01"}, {"name": "NOLB", "label": "NOLB", "type": "number", "min": 0, "step": "0.01"}, {"name": "YouthAglow", "label": "Youth Aglow", "type": "number", "min": 0, "step": "0.01"}, {"name": "LocalDistribution", "label": "Local Distribution", "type": "number", "min": 0, "step": "0.01"}, {"name": "Subscriptions_QUBADS", "label": "Subscriptions QUBADS", "type": "number", "min": 0, "step": "0.01"}, {"name": "OtherProjects", "label": "Other Projects", "type": "number", "min": 0, "step": "0.01"}, {"name": "GrandTotal", "label": "Grand Total", "type": "number", "min": 0, "step": "0.01"}]}