{"title": "External Partners Submission", "description": "Submit data for external partnership initiatives and sponsorships", "entity_type": "External", "fields": [{"name": "organization_name", "label": "Organization Name", "type": "text", "required": true, "placeholder": "Enter organization name"}, {"name": "contact_person", "label": "Contact Person", "type": "text", "required": true, "placeholder": "Enter contact person name"}, {"name": "contact_email", "label": "Contact Email", "type": "email", "required": true, "placeholder": "Enter contact email address"}, {"name": "contact_phone", "label": "Contact Phone", "type": "tel", "required": true, "placeholder": "Enter contact phone number"}, {"name": "organization_type", "label": "Organization Type", "type": "select", "required": false, "options": ["NGO", "Educational Institution", "Business/Company", "Religious Organization", "Community Group", "Other"]}, {"name": "group", "label": "Group", "type": "select", "required": true, "options": "groups"}, {"name": "country", "label": "Country", "type": "select", "required": true, "options": "countries"}, {"name": "money_denomination", "label": "Money Denomination", "type": "radio", "required": true, "options": [{"value": "CURRENCY", "label": "Local Currency"}, {"value": "ESPEES", "label": "ESPEES"}]}, {"name": "money_amount", "label": "Total Amount Received", "type": "number", "required": true, "min": 0, "step": "0.01", "placeholder": "0.00"}, {"name": "currency_code", "label": "Currency Code", "type": "select", "required": false, "options": "currencies", "depends_on": "money_denomination", "show_when": "CURRENCY"}], "program_fields": [{"name": "RhapsodySubscriptions", "label": "Rhapsody Subscriptions", "type": "number", "min": 0, "step": "1"}, {"name": "QUBADS", "label": "QUBADS", "type": "number", "min": 0, "step": "1"}, {"name": "SponsorshipCallCentre", "label": "Sponsorship Call Centre", "type": "number", "min": 0, "step": "1"}, {"name": "QuantitySponsoredREON", "label": "Quantity Sponsored REON", "type": "number", "min": 0, "step": "1"}, {"name": "TranslatorsNetwork", "label": "Translators Network International", "type": "number", "min": 0, "step": "1"}, {"name": "RIN", "label": "Rhapsody Influencers Network", "type": "number", "min": 0, "step": "1"}, {"name": "RIM", "label": "Rhapsody International Missions", "type": "number", "min": 0, "step": "1"}, {"name": "Total", "label": "Total", "type": "number", "min": 0, "step": "1"}]}