{"title": "Campaigns & Sub-Campaigns Submission", "description": "Submit data for outreaches and sub-campaign activities", "entity_type": "Campaigns", "fields": [{"name": "region_id", "label": "Region", "type": "select", "required": false, "options": "regions"}, {"name": "zone", "label": "Zone", "type": "select", "required": false, "options": "zones"}, {"name": "group_name", "label": "Group", "type": "select", "required": false, "options": "groups"}, {"name": "country", "label": "Country", "type": "select", "required": true, "options": "countries"}, {"name": "money_denomination", "label": "Money Denomination", "type": "radio", "required": true, "options": [{"value": "CURRENCY", "label": "Local Currency"}, {"value": "ESPEES", "label": "ESPEES"}]}, {"name": "money_amount", "label": "Total Amount", "type": "number", "required": true, "min": 0, "step": "0.01", "placeholder": "0.00"}, {"name": "currency_code", "label": "Currency Code", "type": "select", "required": false, "options": "currencies", "depends_on": "money_denomination", "show_when": "CURRENCY"}], "program_fields": [{"name": "ReachOutWorld", "label": "Reach Out World", "type": "number", "min": 0, "step": "1"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "min": 0, "step": "1"}, {"name": "SayYesToKids", "label": "Say Yes to Kids", "type": "number", "min": 0, "step": "1"}, {"name": "Teevolution", "label": "Teevolution", "type": "number", "min": 0, "step": "1"}, {"name": "YouthAglow", "label": "Youth Aglow", "type": "number", "min": 0, "step": "1"}, {"name": "NOLB", "label": "NOLB", "type": "number", "min": 0, "step": "1"}, {"name": "AdoptAStreet", "label": "Adopt A Street", "type": "number", "min": 0, "step": "1"}, {"name": "TotalOutreaches", "label": "Total Outreaches", "type": "number", "min": 0, "step": "1"}]}