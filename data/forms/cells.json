{"title": "Cell Programs Submission", "description": "Submit data for cell ministry activities and programs", "entity_type": "Cell", "fields": [{"name": "cell_leader", "label": "Cell Leader", "type": "text", "required": true, "placeholder": "Enter cell leader name"}, {"name": "kingschat_phone", "label": "KingsChat Phone", "type": "tel", "required": true, "placeholder": "Enter KingsChat phone number"}, {"name": "email", "label": "Email", "type": "email", "required": true, "placeholder": "Enter email address"}, {"name": "church", "label": "Church", "type": "text", "required": true, "placeholder": "Enter church name"}, {"name": "group", "label": "Group", "type": "select", "required": true, "options": "groups"}, {"name": "region_id", "label": "Region", "type": "select", "required": false, "options": "regions"}, {"name": "zone", "label": "Zone", "type": "select", "required": false, "options": "zones"}, {"name": "country", "label": "Country", "type": "select", "required": true, "options": "countries"}, {"name": "money_denomination", "label": "Money Denomination", "type": "radio", "required": true, "options": [{"value": "CURRENCY", "label": "Local Currency"}, {"value": "ESPEES", "label": "ESPEES"}]}, {"name": "money_amount", "label": "Total Amount Received", "type": "number", "required": true, "min": 0, "step": "0.01", "placeholder": "0.00"}, {"name": "currency_code", "label": "Currency Code", "type": "select", "required": false, "options": "currencies", "depends_on": "money_denomination", "show_when": "CURRENCY"}], "program_fields": [{"name": "Kiddies", "label": "Kiddies", "type": "number", "min": 0, "step": "0.01"}, {"name": "Teevo", "label": "Teevo", "type": "number", "min": 0, "step": "0.01"}, {"name": "Braille", "label": "Braille", "type": "number", "min": 0, "step": "0.01"}, {"name": "Languages", "label": "Languages", "type": "number", "min": 0, "step": "0.01"}, {"name": "YouthAglow", "label": "Youth Aglow", "type": "number", "min": 0, "step": "0.01"}, {"name": "TotalQuantitySponsored", "label": "Total Quantity Sponsored", "type": "number", "min": 0, "step": "1"}, {"name": "TotalAmountReceived", "label": "Total Amount Received", "type": "number", "min": 0, "step": "0.01"}, {"name": "TotalGiven", "label": "Total Given", "type": "number", "min": 0, "step": "0.01"}]}