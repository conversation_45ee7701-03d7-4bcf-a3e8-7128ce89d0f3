{"categories": [{"id": "campaigns", "label": "Campaigns & Sub-Campaigns", "entity_type": "Campaigns", "grain": "campaign", "programs_allowed": ["ReachOutWorld", "<PERSON><PERSON><PERSON><PERSON>", "SayYesToKids", "Teevolution", "YouthAglow", "NOLB", "AdoptAStreet", "TotalOutreaches"], "form_config": "campaigns.json", "schema_ref": "campaigns_v1.json", "enabled": true}, {"id": "category_b", "label": "Category B - Group Churches", "entity_type": "CategoryB", "grain": "group_church", "programs_allowed": ["Kiddies", "Teevo", "NOLB", "Languages", "YouthAglow", "TotalQuantitySponsored", "TotalAmountReceived"], "form_config": "category_b.json", "schema_ref": "category_b_v1.json", "enabled": true}, {"id": "category_c", "label": "Category C - Local Churches", "entity_type": "CategoryC", "grain": "local_church", "programs_allowed": ["Kiddies", "Teevo", "NOLB", "Languages", "YouthAglow", "TotalQuantitySponsored", "TotalAmountReceived"], "form_config": "category_c.json", "schema_ref": "category_c_v1.json", "enabled": true}, {"id": "cells", "label": "Cells", "entity_type": "Cell", "grain": "cell", "programs_allowed": ["Kiddies", "Teevo", "Braille", "Languages", "YouthAglow", "TotalQuantitySponsored", "TotalAmountReceived", "TotalGiven"], "form_config": "cells.json", "schema_ref": "cells_v1.json", "enabled": true}, {"id": "partners_adult", "label": "Adult Partners", "entity_type": "Adult", "grain": "person", "programs_allowed": ["WonderChallenge", "Languages", "Kiddies", "Teevo", "NOLB", "YouthAglow", "LocalDistribution", "Subscriptions_QUBADS", "OtherProjects", "GrandTotal"], "form_config": "partners_adult.json", "schema_ref": "partners_adult_v1.json", "enabled": true}, {"id": "partners_kids", "label": "Children Partners", "entity_type": "Kids", "grain": "person", "programs_allowed": ["TotalKiddiesROR", "OtherRORProjects", "GrandTotal"], "form_config": "partners_kids.json", "schema_ref": "partners_kids_v1.json", "enabled": true}, {"id": "partners_teens", "label": "Teen Partners", "entity_type": "Teens", "grain": "person", "programs_allowed": ["MonthlyTeevo", "OtherRORProjects", "GrandTotal"], "form_config": "partners_teens.json", "schema_ref": "partners_teens_v1.json", "enabled": true}, {"id": "partners_external", "label": "External Partners", "entity_type": "External", "grain": "organization", "programs_allowed": ["RhapsodySubscriptions", "QUBADS", "SponsorshipCallCentre", "QuantitySponsoredREON", "TranslatorsNetwork", "RIN", "RIM", "Total"], "form_config": "partners_external.json", "schema_ref": "partners_external_v1.json", "enabled": true}], "last_updated": "2025-01-01T00:00:00Z", "version": "1.0"}