<?php
require_once 'controllers/records.php';
require_once 'app/auth.php';

// Simulate RZM user submitting form
$_SESSION['user'] = [
    'id' => 'u_rzm_lagos',
    'role' => 'RZM',
    'region_id' => 4,
    'zone_id' => 'LAGOS ZONE 1',
    'email' => '<EMAIL>'
];

// Simulate form data that an RZM user might submit
$form_data = [
    'entity_type' => 'Kids',
    'name' => 'Test Child',
    'email' => '<EMAIL>',
    'kingschat_phone' => '123456789',
    'group' => 'TEST_GROUP',
    'country' => 'NG',
    // Note: No zone or region_id in form data (as they should be hidden)
];

echo 'Submitting form data...' . PHP_EOL;
$result = submit_record($form_data);

if ($result['success']) {
    echo '✅ Submission successful!' . PHP_EOL;
    echo 'Record ID: ' . $result['record_id'] . PHP_EOL;

    // Check what was actually saved
    $records = db_read('partners_kids');
    $last_record = end($records);
    echo 'Saved record region_id: ' . ($last_record['region_id'] ?? 'NULL') . PHP_EOL;
    echo 'Saved record zone_id: ' . ($last_record['zone_id'] ?? 'NULL') . PHP_EOL;
    echo 'User region_id: ' . $_SESSION['user']['region_id'] . PHP_EOL;
    echo 'User zone_id: ' . $_SESSION['user']['zone_id'] . PHP_EOL;
} else {
    echo '❌ Submission failed!' . PHP_EOL;
    echo 'Errors: ' . json_encode($result['errors'] ?? []) . PHP_EOL;
}
