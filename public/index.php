<?php
/**
 * IPPC Awards Portal - Main Router
 * Single entry point for the application
 */

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('UTC');

// Include core utilities
require_once __DIR__ . '/../app/auth.php';
require_once __DIR__ . '/../app/db.php';
require_once __DIR__ . '/../app/normalize.php';
require_once __DIR__ . '/../app/access.php';
require_once __DIR__ . '/../app/validators.php';

// Get the route parameter
$route = $_GET['r'] ?? 'dashboard';

// Handle logout first (no login required)
if ($route === 'logout') {
    logout();
    header('Location: ?r=login');
    exit;
}

// Handle AJAX requests for zones
if (isset($_GET['ajax']) && $_GET['ajax'] === 'zones') {
    require_login(); // Ensure user is authenticated
    require_once __DIR__ . '/../views/components.php';
    header('Content-Type: application/json');

    $region = $_GET['region'] ?? null;
    if ($region) {
        // Filter zones by selected region
        $zones_data = json_decode(file_get_contents(__DIR__ . '/../zones.json'), true);
        $zones = [];

        if (isset($zones_data[$region])) {
            foreach ($zones_data[$region] as $zone_key => $zone_data) {
                if (isset($zone_data['name'])) {
                    $zones[$zone_key] = $zone_data['name'];
                } else {
                    $zones[$zone_key] = $zone_key;
                }
            }
        }

        // Sort zones alphabetically
        asort($zones);
        echo json_encode(['zones' => $zones]);
    } else {
        // Return all zones
        $zones = get_lookup_options('zones');
        echo json_encode(['zones' => $zones]);
    }
    exit;
}

// Handle AJAX requests for groups
if (isset($_GET['ajax']) && $_GET['ajax'] === 'groups' && isset($_GET['zone'])) {
    require_login(); // Ensure user is authenticated
    require_once __DIR__ . '/../views/components.php';
    header('Content-Type: application/json');

    // Set the zone in GET parameters for the lookup function
    $_GET['zone'] = $_GET['zone'];
    $groups = get_lookup_options('groups');
    echo json_encode(['groups' => $groups]);
    exit;
}

// Handle AJAX requests for search (DataTable)
if (isset($_GET['r']) && $_GET['r'] === 'search') {
    require_login(); // Ensure user is authenticated
    header('Content-Type: application/json');
    require_once __DIR__ . '/../controllers/search.php';

    try {
        $result = search($_GET);
        echo json_encode($result);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
    exit;
}

// Handle AJAX requests for top entities
if (isset($_GET['r']) && $_GET['r'] === 'top') {
    require_login(); // Ensure user is authenticated
    header('Content-Type: application/json');
    require_once __DIR__ . '/../controllers/search.php';

    try {
        $result = top_entity($_GET);
        echo json_encode($result);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
    exit;
}

// Routes that don't require authentication
$public_routes = ['login', 'register', 'pending_approval'];

// Check authentication for protected routes
if (!in_array($route, $public_routes)) {
    require_login();
}

// Route handling
switch ($route) {
    // Authentication
    case 'login':
        include __DIR__ . '/../views/pages/login.php';
        break;

    case 'register':
        include __DIR__ . '/../views/pages/register.php';
        break;

    case 'pending_approval':
        include __DIR__ . '/../views/pages/pending_approval.php';
        break;

    case 'profile':
        include __DIR__ . '/../views/pages/profile.php';
        break;

    // Dashboard
    case 'dashboard':
        include __DIR__ . '/../views/pages/dashboard.php';
        break;
        
    // Submission Forms
    case 'submit_campaigns':
        include __DIR__ . '/../views/pages/submit_campaigns.php';
        break;



    case 'submit_category_b':
        include __DIR__ . '/../views/pages/submit_category_b.php';
        break;

    case 'submit_category_c':
        include __DIR__ . '/../views/pages/submit_category_c.php';
        break;

    case 'submit_cells':
        include __DIR__ . '/../views/pages/submit_cells.php';
        break;

    case 'submit_adult':
        include __DIR__ . '/../views/pages/submit_adult.php';
        break;

    case 'submit_kids':
        include __DIR__ . '/../views/pages/submit_kids.php';
        break;

    case 'submit_teens':
        include __DIR__ . '/../views/pages/submit_teens.php';
        break;

    case 'submit_external':
        include __DIR__ . '/../views/pages/submit_external.php';
        break;
        
        // Admin Pages (require admin access)
    case 'admin_users':
        require_admin();
        include __DIR__ . '/../views/pages/admin/users.php';
        break;

    case 'admin_user_edit':
        require_admin();
        include __DIR__ . '/../views/pages/admin/user_edit.php';
        break;



    case 'admin_settings':
        require_admin();
        include __DIR__ . '/../views/pages/admin/settings.php';
        break;

    case 'admin_stats':
        require_admin();
        // Placeholder for system statistics page
        echo "System statistics page would be implemented here";
        break;
        

        
    case 'submit':
        header('Content-Type: application/json');
        require_once __DIR__ . '/../controllers/records.php';
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }
        
        try {
            $result = submit_record($_POST);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;
        
    case 'submit_for_approval':
        header('Content-Type: application/json');
        require_once __DIR__ . '/../controllers/records.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $record_id = $_POST['record_id'] ?? '';
            $result = submit_for_approval($record_id);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    case 'update_record':
        header('Content-Type: application/json');
        require_once __DIR__ . '/../controllers/records.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $record_id = $_POST['record_id'] ?? '';
            $result = update_record($record_id, $_POST);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;
        

        
    case 'save_registry':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }
        
        try {
            $registry_type = $_POST['registry_type'] ?? '';
            $data = json_decode($_POST['registry_data'] ?? '{}', true);
            $result = save_registry($registry_type, $data);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;
        
    case 'recompute':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }
        
        try {
            $result = recompute_normalizations();
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;
        
    case 'backup':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $result = create_backup();
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    // User Management API Endpoints
    case 'create_user':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $result = create_user($_POST);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    case 'update_user':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $user_id = $_POST['user_id'] ?? '';
            unset($_POST['user_id']); // Remove from data array
            $result = update_user($user_id, $_POST);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    case 'delete_user':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $user_id = $_POST['user_id'] ?? '';
            $result = delete_user($user_id);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    case 'toggle_user_status':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $user_id = $_POST['user_id'] ?? '';
            $status = $_POST['status'] ?? '';
            $result = toggle_user_status($user_id, $status);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    case 'approve_user':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $user_id = $_POST['user_id'] ?? '';
            $result = approve_user($user_id);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    case 'deny_user':
        header('Content-Type: application/json');
        require_admin();
        require_once __DIR__ . '/../controllers/admin.php';

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
        }

        try {
            $user_id = $_POST['user_id'] ?? '';
            $result = deny_user($user_id);
            echo json_encode($result);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;
        
    case 'record_details':
        header('Content-Type: application/json');
        require_once __DIR__ . '/../controllers/records.php';

        try {
            $record_id = $_GET['id'] ?? '';
            $record = find_record_by_id($record_id);

            if ($record) {
                // Apply scope restrictions
                $scoped_records = apply_scope([$record]);
                if (empty($scoped_records)) {
                    echo json_encode(['success' => false, 'error' => 'Record not found or access denied']);
                } else {
                    echo json_encode(['success' => true, 'record' => $record]);
                }
            } else {
                echo json_encode(['success' => false, 'error' => 'Record not found']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        break;

    case 'edit_record':
        require_once __DIR__ . '/../controllers/records.php';
        $record_id = $_GET['id'] ?? '';

        if (!$record_id) {
            header('Location: ?r=dashboard');
            exit;
        }

        try {
            $record = find_record_by_id($record_id);

            if (!$record) {
                header('Location: ?r=dashboard&error=Record not found');
                exit;
            }

            // Apply scope restrictions
            $scoped_records = apply_scope([$record]);
            if (empty($scoped_records)) {
                header('Location: ?r=dashboard&error=Access denied');
                exit;
            }

            // Use the generic edit form for all entity types
            include __DIR__ . '/../views/pages/edit_record.php';
        } catch (Exception $e) {
            header('Location: ?r=dashboard&error=' . urlencode($e->getMessage()));
            exit;
        }
        break;
        
    // Default case - 404
    default:
        http_response_code(404);
        
        if (str_starts_with($route, 'api/') || isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Route not found']);
        } else {
            echo "<!DOCTYPE html>
            <html lang='en'>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>404 - Page Not Found</title>
                <script src='https://cdn.tailwindcss.com'></script>
            </head>
            <body class='bg-gray-50 flex items-center justify-center min-h-screen'>
                <div class='text-center'>
                    <h1 class='text-6xl font-bold text-gray-900 mb-4'>404</h1>
                    <p class='text-xl text-gray-600 mb-8'>Page not found</p>
                    <a href='?r=dashboard' class='bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg'>
                        Back to Dashboard
                    </a>
                </div>
            </body>
            </html>";
        }
        break;
}
?>
